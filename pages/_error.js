function Error({ statusCode }) {
  return (
    <div style={{
      fontFamily: 'system-ui, "Segoe UI", Roboto, Helvetica, Arial, sans-serif',
      height: '100vh',
      textAlign: 'center',
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center'
    }}>
      <div>
        <style dangerouslySetInnerHTML={{
          __html: `
            body { color: #000; background: #fff; margin: 0; }
            .next-error-h1 { border-right: 1px solid rgba(0, 0, 0, .3); }
            @media (prefers-color-scheme: dark) {
              body { color: #fff; background: #000; }
              .next-error-h1 { border-right: 1px solid rgba(255, 255, 255, .3); }
            }
          `
        }} />
        {statusCode ? (
          <h1 className="next-error-h1" style={{
            display: 'inline-block',
            margin: '0 20px 0 0',
            paddingRight: 23,
            fontSize: 24,
            fontWeight: 500,
            verticalAlign: 'top'
          }}>
            {statusCode}
          </h1>
        ) : null}
        <div style={{ display: 'inline-block' }}>
          <h2 style={{
            fontSize: 14,
            fontWeight: 400,
            lineHeight: '28px'
          }}>
            {statusCode === 404
              ? 'Cette page n\'a pas pu être trouvée.'
              : statusCode === 500
              ? 'Une erreur interne du serveur s\'est produite.'
              : 'Une erreur inattendue s\'est produite.'
            }
          </h2>
        </div>
      </div>
    </div>
  )
}

Error.getInitialProps = ({ res, err }) => {
  const statusCode = res ? res.statusCode : err ? err.statusCode : 404
  return { statusCode }
}

export default Error
