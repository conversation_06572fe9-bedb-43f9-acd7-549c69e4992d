-- Création des tables

-- Table ENTREPRISE
CREATE TABLE IF NOT EXISTS entreprises (
  id SERIAL PRIMARY KEY,
  nom VARCHAR(255) NOT NULL,
  siret VARCHAR(14),
  adresse TEXT,
  code_postal VARCHAR(10),
  ville VARCHAR(100),
  telephone VARCHAR(20),
  email VARCHAR(255),
  logo_url TEXT,
  site_web VARCHAR(255),
  cgv TEXT,
  date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  devise VARCHAR(3) DEFAULT 'EUR',
  actif BOOLEAN DEFAULT TRUE
);

-- Table UTILISATEUR
CREATE TABLE IF NOT EXISTS utilisateurs (
  id SERIAL PRIMARY KEY,
  entreprise_id INTEGER REFERENCES entreprises(id),
  nom VARCHAR(100),
  prenom VARCHAR(100),
  email VARCHAR(255) NOT NULL UNIQUE,
  telephone VARCHAR(20),
  role VARCHAR(50) DEFAULT 'collaborateur',
  mot_de_passe_hash VARCHAR(255),
  derniere_connexion TIMESTAMP,
  actif BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table CLIENT
CREATE TABLE IF NOT EXISTS clients (
  id SERIAL PRIMARY KEY,
  entreprise_id INTEGER REFERENCES entreprises(id),
  type VARCHAR(20) CHECK (type IN ('particulier', 'professionnel')),
  nom VARCHAR(100),
  prenom VARCHAR(100),
  societe VARCHAR(255),
  siret VARCHAR(14),
  adresse_facturation TEXT,
  adresse_chantier TEXT,
  telephone VARCHAR(20),
  email VARCHAR(255),
  notes TEXT,
  date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  actif BOOLEAN DEFAULT TRUE
);

-- Table CATALOGUE
CREATE TABLE IF NOT EXISTS catalogue (
  id SERIAL PRIMARY KEY,
  entreprise_id INTEGER REFERENCES entreprises(id),
  nom VARCHAR(255) NOT NULL,
  type VARCHAR(50) CHECK (type IN ('matériaux', 'main d''œuvre', 'prestation')),
  reference VARCHAR(50),
  description TEXT,
  prix_unitaire_ht DECIMAL(10, 2) NOT NULL,
  unite VARCHAR(20),
  taux_tva DECIMAL(5, 2) DEFAULT 20,
  actif BOOLEAN DEFAULT TRUE,
  categorie VARCHAR(100),
  sous_categorie VARCHAR(100)
);

-- Table PROJET
CREATE TABLE IF NOT EXISTS projets (
  id SERIAL PRIMARY KEY,
  client_id INTEGER REFERENCES clients(id),
  utilisateur_id INTEGER REFERENCES utilisateurs(id),
  reference VARCHAR(50) UNIQUE,
  nom VARCHAR(255) NOT NULL,
  adresse_chantier TEXT,
  date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  date_debut_prevu DATE,
  date_fin_prevu DATE,
  date_debut_reel DATE,
  date_fin_reel DATE,
  statut VARCHAR(20) CHECK (statut IN ('devis', 'signé', 'en_cours', 'terminé', 'facturé')),
  description TEXT,
  montant_total_ht DECIMAL(10, 2),
  montant_total_ttc DECIMAL(10, 2)
);

-- Table DEVIS
CREATE TABLE IF NOT EXISTS devis (
  id SERIAL PRIMARY KEY,
  projet_id INTEGER REFERENCES projets(id),
  utilisateur_id INTEGER REFERENCES utilisateurs(id),
  reference VARCHAR(50) UNIQUE,
  date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  date_validite DATE,
  date_signature TIMESTAMP,
  statut VARCHAR(20) CHECK (statut IN ('brouillon', 'envoyé', 'signé', 'refusé')),
  introduction TEXT,
  conclusion TEXT,
  taux_remise DECIMAL(5, 2) DEFAULT 0,
  montant_ht DECIMAL(10, 2),
  montant_ttc DECIMAL(10, 2),
  validite_jours INTEGER DEFAULT 30,
  conditions_acceptees BOOLEAN DEFAULT FALSE,
  signature_url TEXT
);
