-- C<PERSON>er le bucket de stockage pour les fichiers clients
INSERT INTO storage.buckets (id, name, public)
VALUES ('client-files', 'client-files', true);

-- Ajouter les politiques d'accès pour les utilisateurs authentifiés
-- Permettre aux utilisateurs authentifiés d'uploader des fichiers
CREATE POLICY "Allow authenticated users to upload files"
ON storage.objects FOR INSERT TO authenticated USING (
  bucket_id = 'client-files' AND auth.role() = 'authenticated'
);

-- Permettre aux utilisateurs authentifiés de voir les fichiers
CREATE POLICY "Allow users to select their own files"
ON storage.objects FOR SELECT TO authenticated USING (
  bucket_id = 'client-files'
);

-- Permettre aux utilisateurs authentifiés de mettre à jour leurs fichiers
CREATE POLICY "Allow users to update their own files"
ON storage.objects FOR UPDATE TO authenticated USING (
  bucket_id = 'client-files'
);

-- Permettre aux utilisateurs authentifiés de supprimer leurs fichiers
CREATE POLICY "Allow users to delete their own files"
ON storage.objects FOR DELETE TO authenticated USING (
  bucket_id = 'client-files'
);
