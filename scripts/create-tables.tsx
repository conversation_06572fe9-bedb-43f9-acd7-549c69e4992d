"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { supabase } from "@/lib/supabase"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { CheckCircle2, XCircle } from "lucide-react"

export default function CreateTables() {
  const [isCreating, setIsCreating] = useState(false)
  const [results, setResults] = useState<{ table: string; success: boolean; message: string }[]>([])
  const [isComplete, setIsComplete] = useState(false)

  const createTables = async () => {
    setIsCreating(true)
    setResults([])

    try {
      // Create ENTREPRISE table
      await createTable(
        "ENTREPRISE",
        `
        CREATE TABLE IF NOT EXISTS entreprises (
          id SERIAL PRIMARY KEY,
          nom VARCHAR(255) NOT NULL,
          siret VARCHAR(14),
          ad<PERSON><PERSON>,
          code_postal VARCHAR(10),
          ville VARCHA<PERSON>(100),
          telephone VARCHAR(20),
          email VARCHAR(255),
          logo_url TEXT,
          site_web VARCHAR(255),
          cgv TEXT,
          date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          devise VARCHAR(3) DEFAULT 'EUR',
          actif BOOLEAN DEFAULT TRUE
        );
      `,
      )

      // Create UTILISATEUR table
      await createTable(
        "UTILISATEUR",
        `
        CREATE TABLE IF NOT EXISTS utilisateurs (
          id SERIAL PRIMARY KEY,
          entreprise_id INTEGER REFERENCES entreprises(id),
          nom VARCHAR(100),
          prenom VARCHAR(100),
          email VARCHAR(255) NOT NULL UNIQUE,
          telephone VARCHAR(20),
          role VARCHAR(50) DEFAULT 'collaborateur',
          mot_de_passe_hash VARCHAR(255),
          derniere_connexion TIMESTAMP,
          actif BOOLEAN DEFAULT TRUE,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
      `,
      )

      // Create CLIENT table
      await createTable(
        "CLIENT",
        `
        CREATE TABLE IF NOT EXISTS clients (
          id SERIAL PRIMARY KEY,
          entreprise_id INTEGER REFERENCES entreprises(id),
          type VARCHAR(20) CHECK (type IN ('particulier', 'professionnel')),
          nom VARCHAR(100),
          prenom VARCHAR(100),
          societe VARCHAR(255),
          siret VARCHAR(14),
          adresse_facturation TEXT,
          adresse_chantier TEXT,
          telephone VARCHAR(20),
          email VARCHAR(255),
          notes TEXT,
          date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          actif BOOLEAN DEFAULT TRUE
        );
      `,
      )

      // Create PROJET table
      await createTable(
        "PROJET",
        `
        CREATE TABLE IF NOT EXISTS projets (
          id SERIAL PRIMARY KEY,
          client_id INTEGER REFERENCES clients(id),
          utilisateur_id INTEGER REFERENCES utilisateurs(id),
          reference VARCHAR(50) UNIQUE,
          nom VARCHAR(255) NOT NULL,
          adresse_chantier TEXT,
          date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          date_debut_prevu DATE,
          date_fin_prevu DATE,
          date_debut_reel DATE,
          date_fin_reel DATE,
          statut VARCHAR(20) CHECK (statut IN ('devis', 'signé', 'en_cours', 'terminé', 'facturé')),
          description TEXT,
          montant_total_ht DECIMAL(10, 2),
          montant_total_ttc DECIMAL(10, 2)
        );
      `,
      )

      // Create DEVIS table
      await createTable(
        "DEVIS",
        `
        CREATE TABLE IF NOT EXISTS devis (
          id SERIAL PRIMARY KEY,
          projet_id INTEGER REFERENCES projets(id),
          utilisateur_id INTEGER REFERENCES utilisateurs(id),
          reference VARCHAR(50) UNIQUE,
          date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          date_validite DATE,
          date_signature TIMESTAMP,
          statut VARCHAR(20) CHECK (statut IN ('brouillon', 'envoyé', 'signé', 'refusé')),
          introduction TEXT,
          conclusion TEXT,
          taux_remise DECIMAL(5, 2) DEFAULT 0,
          montant_ht DECIMAL(10, 2),
          montant_ttc DECIMAL(10, 2),
          validite_jours INTEGER DEFAULT 30,
          conditions_acceptees BOOLEAN DEFAULT FALSE,
          signature_url TEXT
        );
      `,
      )

      // Create SECTION_DEVIS table
      await createTable(
        "SECTION_DEVIS",
        `
        CREATE TABLE IF NOT EXISTS sections_devis (
          id SERIAL PRIMARY KEY,
          devis_id INTEGER REFERENCES devis(id),
          titre VARCHAR(255) NOT NULL,
          description TEXT,
          ordre INTEGER,
          montant_ht DECIMAL(10, 2)
        );
      `,
      )

      // Create CATALOGUE table
      await createTable(
        "CATALOGUE",
        `
        CREATE TABLE IF NOT EXISTS catalogue (
          id SERIAL PRIMARY KEY,
          entreprise_id INTEGER REFERENCES entreprises(id),
          nom VARCHAR(255) NOT NULL,
          type VARCHAR(50) CHECK (type IN ('matériaux', 'main d''œuvre', 'prestation')),
          reference VARCHAR(50),
          description TEXT,
          prix_unitaire_ht DECIMAL(10, 2) NOT NULL,
          unite VARCHAR(20),
          taux_tva DECIMAL(5, 2) DEFAULT 20,
          actif BOOLEAN DEFAULT TRUE,
          categorie VARCHAR(100),
          sous_categorie VARCHAR(100)
        );
      `,
      )

      // Create LIGNE_DEVIS table
      await createTable(
        "LIGNE_DEVIS",
        `
        CREATE TABLE IF NOT EXISTS lignes_devis (
          id SERIAL PRIMARY KEY,
          section_id INTEGER REFERENCES sections_devis(id),
          catalogue_item_id INTEGER REFERENCES catalogue(id),
          designation VARCHAR(255) NOT NULL,
          reference VARCHAR(50),
          description TEXT,
          quantite DECIMAL(10, 3) NOT NULL,
          unite VARCHAR(20),
          prix_unitaire_ht DECIMAL(10, 2) NOT NULL,
          taux_tva DECIMAL(5, 2) DEFAULT 20,
          montant_ht DECIMAL(10, 2),
          montant_ttc DECIMAL(10, 2),
          ordre INTEGER
        );
      `,
      )

      // Create FACTURE table
      await createTable(
        "FACTURE",
        `
        CREATE TABLE IF NOT EXISTS factures (
          id SERIAL PRIMARY KEY,
          devis_id INTEGER REFERENCES devis(id),
          reference VARCHAR(50) UNIQUE,
          type VARCHAR(20) CHECK (type IN ('acompte', 'situation', 'solde')),
          date_emission TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          date_echeance DATE,
          montant_ht DECIMAL(10, 2),
          montant_ttc DECIMAL(10, 2),
          statut VARCHAR(20) CHECK (statut IN ('brouillon', 'envoyée', 'payée', 'impayée')),
          date_paiement TIMESTAMP,
          mode_paiement VARCHAR(50)
        );
      `,
      )

      // Create DOCUMENT table
      await createTable(
        "DOCUMENT",
        `
        CREATE TABLE IF NOT EXISTS documents (
          id SERIAL PRIMARY KEY,
          projet_id INTEGER REFERENCES projets(id),
          utilisateur_id INTEGER REFERENCES utilisateurs(id),
          type VARCHAR(20) CHECK (type IN ('photo', 'note', 'audio', 'pdf', 'plan')),
          nom_fichier VARCHAR(255),
          url_stockage TEXT,
          mimetype VARCHAR(100),
          date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          description TEXT,
          tags VARCHAR(255),
          transcript TEXT,
          taille_ko DECIMAL(10, 2)
        );
      `,
      )

      // Create NOTE table
      await createTable(
        "NOTE",
        `
        CREATE TABLE IF NOT EXISTS notes (
          id SERIAL PRIMARY KEY,
          projet_id INTEGER REFERENCES projets(id),
          utilisateur_id INTEGER REFERENCES utilisateurs(id),
          contenu TEXT,
          date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          date_modification TIMESTAMP,
          type VARCHAR(20) CHECK (type IN ('manuscrite', 'texte', 'transcription')),
          convertie_devis BOOLEAN DEFAULT FALSE
        );
      `,
      )

      // Create CCTP_DOCUMENT table
      await createTable(
        "CCTP_DOCUMENT",
        `
        CREATE TABLE IF NOT EXISTS cctp_documents (
          id SERIAL PRIMARY KEY,
          projet_id INTEGER REFERENCES projets(id),
          reference VARCHAR(50),
          titre VARCHAR(255),
          contenu TEXT,
          date_import TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
      `,
      )

      // Create CCTP_SECTION table
      await createTable(
        "CCTP_SECTION",
        `
        CREATE TABLE IF NOT EXISTS cctp_sections (
          id SERIAL PRIMARY KEY,
          cctp_document_id INTEGER REFERENCES cctp_documents(id),
          numero VARCHAR(20),
          titre VARCHAR(255),
          contenu TEXT,
          parent_id INTEGER REFERENCES cctp_sections(id)
        );
      `,
      )

      // Create NOTIFICATION table
      await createTable(
        "NOTIFICATION",
        `
        CREATE TABLE IF NOT EXISTS notifications (
          id SERIAL PRIMARY KEY,
          utilisateur_id INTEGER REFERENCES utilisateurs(id),
          type VARCHAR(50),
          titre VARCHAR(255),
          message TEXT,
          date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          lu BOOLEAN DEFAULT FALSE,
          lien VARCHAR(255)
        );
      `,
      )

      setIsComplete(true)
    } catch (error) {
      console.error("Error creating tables:", error)
    } finally {
      setIsCreating(false)
    }
  }

  const createTable = async (tableName: string, sql: string) => {
    try {
      const { error } = await supabase.rpc("exec_sql", { sql_query: sql })

      if (error) {
        setResults((prev) => [...prev, { table: tableName, success: false, message: error.message }])
        return false
      }

      setResults((prev) => [
        ...prev,
        { table: tableName, success: true, message: `Table ${tableName} created successfully` },
      ])
      return true
    } catch (error: any) {
      setResults((prev) => [...prev, { table: tableName, success: false, message: error.message }])
      return false
    }
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Création des Tables de Base de Données</CardTitle>
        <CardDescription>
          Créez toutes les tables nécessaires pour le SaaS BTP dans votre base de données Supabase
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <p className="text-sm text-muted-foreground">
            Ce script va créer les tables suivantes dans votre base de données Supabase :
          </p>
          <ul className="list-disc pl-5 text-sm">
            <li>ENTREPRISE - Informations sur l'entreprise</li>
            <li>UTILISATEUR - Utilisateurs du système</li>
            <li>CLIENT - Clients (particuliers et professionnels)</li>
            <li>PROJET - Projets et chantiers</li>
            <li>DEVIS - Devis émis</li>
            <li>SECTION_DEVIS - Sections des devis</li>
            <li>LIGNE_DEVIS - Lignes détaillées des devis</li>
            <li>CATALOGUE - Catalogue de prix</li>
            <li>FACTURE - Factures émises</li>
            <li>DOCUMENT - Documents et fichiers</li>
            <li>NOTE - Notes et transcriptions</li>
            <li>CCTP_DOCUMENT - Documents CCTP</li>
            <li>CCTP_SECTION - Sections des documents CCTP</li>
            <li>NOTIFICATION - Notifications système</li>
          </ul>

          {results.length > 0 && (
            <div className="mt-4 space-y-2">
              <h3 className="font-medium">Résultats :</h3>
              <div className="max-h-60 overflow-y-auto rounded border p-2">
                {results.map((result, index) => (
                  <div key={index} className="flex items-center gap-2 py-1">
                    {result.success ? (
                      <CheckCircle2 className="h-5 w-5 text-green-500" />
                    ) : (
                      <XCircle className="h-5 w-5 text-red-500" />
                    )}
                    <div>
                      <span className="font-medium">{result.table}: </span>
                      <span className={result.success ? "text-green-600" : "text-red-600"}>{result.message}</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </CardContent>
      <CardFooter>
        <Button onClick={createTables} disabled={isCreating} className="w-full">
          {isCreating ? "Création des tables en cours..." : isComplete ? "Recréer les tables" : "Créer les tables"}
        </Button>
      </CardFooter>
    </Card>
  )
}
