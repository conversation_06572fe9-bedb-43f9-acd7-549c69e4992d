-- Table for client requests
CREATE TABLE IF NOT EXISTS requests (
  id SERIAL PRIMARY KEY,
  client_id INTEGER REFERENCES clients(id) NOT NULL,
  description TEXT NOT NULL,
  status VARCHAR(20) CHECK (status IN ('nouveau', 'en_analyse', 'accepté', 'refusé', 'en_cours', 'terminé')) DEFAULT 'nouveau',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table for plans (PDF or images)
CREATE TABLE IF NOT EXISTS plans (
  id SERIAL PRIMARY KEY,
  request_id INTEGER REFERENCES requests(id) NOT NULL,
  file_path TEXT NOT NULL,
  file_name VARCHAR(255) NOT NULL,
  file_type VARCHAR(50) NOT NULL,
  description TEXT,
  uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table for photos
CREATE TABLE IF NOT EXISTS photos (
  id SERIAL PRIMARY KEY,
  request_id INTEGER REFERENCES requests(id) NOT NULL,
  file_path TEXT NOT NULL,
  file_name VARCHAR(255) NOT NULL,
  description TEXT,
  uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_requests_client_id ON requests(client_id);
CREATE INDEX IF NOT EXISTS idx_plans_request_id ON plans(request_id);
CREATE INDEX IF NOT EXISTS idx_photos_request_id ON photos(request_id);

-- Function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
   NEW.updated_at = NOW();
   RETURN NEW;
END;
$$ LANGUAGE 'plpgsql';

-- Trigger to automatically update the updated_at column
CREATE TRIGGER update_requests_updated_at
BEFORE UPDATE ON requests
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();
