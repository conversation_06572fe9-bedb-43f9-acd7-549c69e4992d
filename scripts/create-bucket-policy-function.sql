-- Function to get policies for a specific bucket
CREATE OR REPLACE FUNCTION get_policies_for_bucket(bucket_name TEXT)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  policies JSONB;
BEGIN
  SELECT jsonb_agg(jsonb_build_object(
    'id', p.id,
    'name', p.name,
    'definition', p.definition,
    'operation', p.operation,
    'role', p.role
  ))
  INTO policies
  FROM storage.policies p
  WHERE p.definition::TEXT LIKE '%' || bucket_name || '%';
  
  RETURN COALESCE(policies, '[]'::JSONB);
END;
$$;
