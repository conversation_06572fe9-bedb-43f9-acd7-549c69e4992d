-- Table to store AI agent analysis results
CREATE TABLE IF NOT EXISTS agent (
  id SERIAL PRIMARY KEY,
  request_id INTEGER REFERENCES requests(id) NOT NULL UNIQUE,
  agent_response TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create index for request_id
CREATE INDEX IF NOT EXISTS idx_agent_request_id ON agent(request_id);

-- Function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
   NEW.updated_at = NOW();
   RETURN NEW;
END;
$$ LANGUAGE 'plpgsql';

-- Trigger to automatically update the updated_at column
CREATE TRIGGER update_agent_updated_at
BEFORE UPDATE ON agent
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();
