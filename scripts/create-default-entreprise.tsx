"use client"

import type React from "react"

import { useEffect, useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { AlertCircle } from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { createEntreprise, getEntreprises } from "@/services/entreprises"

export default function CreateDefaultEntreprise() {
  const { toast } = useToast()
  const [isLoading, setIsLoading] = useState(false)
  const [isChecking, setIsChecking] = useState(true)
  const [hasEntreprise, setHasEntreprise] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [formData, setFormData] = useState({
    nom: "Mon Entreprise BTP",
    siret: "",
    adresse: "",
    code_postal: "",
    ville: "",
    telephone: "",
    email: "",
  })

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const checkEntreprise = async () => {
    setIsChecking(true)
    setError(null)
    try {
      const entreprises = await getEntreprises()
      setHasEntreprise(entreprises.length > 0)
    } catch (err: any) {
      console.error("Erreur lors de la vérification des entreprises:", err)
      setError(err?.message || "Erreur de connexion à la base de données")
    } finally {
      setIsChecking(false)
    }
  }

  // Vérifier s'il existe déjà une entreprise au chargement
  useEffect(() => {
    checkEntreprise()
  }, [])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setError(null)

    try {
      const entrepriseData = {
        nom: formData.nom,
        siret: formData.siret || null,
        adresse: formData.adresse || null,
        code_postal: formData.code_postal || null,
        ville: formData.ville || null,
        telephone: formData.telephone || null,
        email: formData.email || null,
        logo_url: null,
        site_web: null,
        cgv: null,
        devise: "EUR",
        actif: true,
      }

      await createEntreprise(entrepriseData)

      toast({
        title: "Entreprise créée avec succès",
        description: "Votre entreprise a été configurée dans le système",
      })

      setHasEntreprise(true)
    } catch (err: any) {
      setError(err?.message || "Une erreur est survenue lors de la création de l'entreprise")
      toast({
        title: "Erreur",
        description: err?.message || "Une erreur est survenue lors de la création de l'entreprise",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  if (isChecking) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Vérification de la configuration...</p>
        </div>
      </div>
    )
  }

  if (hasEntreprise) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Entreprise configurée</CardTitle>
          <CardDescription>Votre entreprise est déjà configurée dans le système</CardDescription>
        </CardHeader>
        <CardContent>
          <p>Vous pouvez maintenant utiliser toutes les fonctionnalités de l'application.</p>
        </CardContent>
        <CardFooter>
          <Button onClick={() => (window.location.href = "/dashboard")}>Aller au tableau de bord</Button>
        </CardFooter>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Configuration initiale</CardTitle>
        <CardDescription>Configurez votre entreprise pour commencer à utiliser l'application</CardDescription>
      </CardHeader>
      {error && (
        <CardContent className="pt-0 pb-4">
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Erreur</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        </CardContent>
      )}
      <form onSubmit={handleSubmit}>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="nom">Nom de l'entreprise</Label>
            <Input
              id="nom"
              name="nom"
              value={formData.nom}
              onChange={handleChange}
              placeholder="Mon Entreprise BTP"
              required
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="siret">SIRET</Label>
            <Input
              id="siret"
              name="siret"
              value={formData.siret}
              onChange={handleChange}
              placeholder="12345678901234"
              maxLength={14}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="adresse">Adresse</Label>
            <Input
              id="adresse"
              name="adresse"
              value={formData.adresse}
              onChange={handleChange}
              placeholder="123 rue des Artisans"
            />
          </div>
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="code_postal">Code postal</Label>
              <Input
                id="code_postal"
                name="code_postal"
                value={formData.code_postal}
                onChange={handleChange}
                placeholder="75001"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="ville">Ville</Label>
              <Input id="ville" name="ville" value={formData.ville} onChange={handleChange} placeholder="Paris" />
            </div>
          </div>
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="telephone">Téléphone</Label>
              <Input
                id="telephone"
                name="telephone"
                value={formData.telephone}
                onChange={handleChange}
                placeholder="01 23 45 67 89"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                name="email"
                type="email"
                value={formData.email}
                onChange={handleChange}
                placeholder="<EMAIL>"
              />
            </div>
          </div>
        </CardContent>
        <CardFooter className="flex justify-between">
          <Button variant="outline" type="button" onClick={checkEntreprise} disabled={isLoading}>
            Vérifier à nouveau
          </Button>
          <Button type="submit" disabled={isLoading}>
            {isLoading ? "Configuration en cours..." : "Configurer mon entreprise"}
          </Button>
        </CardFooter>
      </form>
    </Card>
  )
}
