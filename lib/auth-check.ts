import { supabase } from "./supabase"

export async function checkAuth() {
  if (!supabase) {
    return {
      authenticated: false,
      error: "Client Supabase non initialisé",
    }
  }

  try {
    const { data, error } = await supabase.auth.getSession()

    if (error) {
      return {
        authenticated: false,
        error: error.message,
      }
    }

    return {
      authenticated: !!data.session,
      session: data.session,
      error: null,
    }
  } catch (error) {
    return {
      authenticated: false,
      error: error?.message || "Erreur lors de la vérification de l'authentification",
    }
  }
}
