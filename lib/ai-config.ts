import { google } from "@ai-sdk/google"

// AI SDK 5 Alpha configuration
export const aiConfig = {
  // Use Gemini 2.5 Pro (currently available as gemini-2.0-flash-exp)
  model: google("gemini-2.0-flash-exp"),

  // Default settings for analysis
  analysisSettings: {
    maxTokens: 4000,
    temperature: 0.7,
    topP: 0.9,
  },

  // Default settings for structured output (like devis conversion)
  structuredSettings: {
    maxTokens: 2000,
    temperature: 0.3,
    topP: 0.8,
  },
}

// Enhanced error handling for AI SDK 5
export function handleAIError(error: any): string {
  if (error.name === "AI_APICallError") {
    return `Erreur API IA: ${error.message}`
  }

  if (error.name === "AI_InvalidPromptError") {
    return `Prompt invalide: ${error.message}`
  }

  if (error.name === "AI_NoSuchModelError") {
    return `Modèle non trouvé: ${error.message}`
  }

  if (error.name === "AI_TooManyEmbeddingValuesError") {
    return `Trop de valeurs d'embedding: ${error.message}`
  }

  return `Erreur IA inconnue: ${error.message || "Une erreur est survenue"}`
}
