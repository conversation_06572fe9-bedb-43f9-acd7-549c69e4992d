// Stockage global de l'état du mode démo
export const demoMode = {
  enabled: false, // Désactivé pour utiliser les vraies données de Supabase
}

// Données de démonstration pour différentes entités (conservées pour référence)
export const demoData = {
  entreprises: [
    {
      id: 1,
      nom: "Entreprise de démonstration",
      siret: "12345678901234",
      adresse: "123 rue de la Démo",
      code_postal: "75000",
      ville: "Paris",
      telephone: "01 23 45 67 89",
      email: "<EMAIL>",
      date_creation: new Date().toISOString(),
      devise: "EUR",
      actif: true,
    },
  ],
  clients: [
    {
      id: 1,
      entreprise_id: 1,
      type: "particulier",
      nom: "Dupont",
      prenom: "<PERSON>",
      email: "<EMAIL>",
      telephone: "06 12 34 56 78",
      adresse_facturation: "456 avenue du Client, 75001 Paris",
      date_creation: new Date().toISOString(),
      actif: true,
    },
    {
      id: 2,
      entreprise_id: 1,
      type: "professionnel",
      societe: "Entreprise ABC",
      siret: "98765432109876",
      email: "<EMAIL>",
      telephone: "01 87 65 43 21",
      adresse_facturation: "789 boulevard Pro, 75002 Paris",
      date_creation: new Date().toISOString(),
      actif: true,
    },
  ],
  catalogue: [
    {
      id: 1,
      entreprise_id: 1,
      nom: "Ciment Portland",
      type: "matériaux",
      reference: "MAT-001",
      description: "Sac de ciment Portland 35kg",
      prix_unitaire_ht: 12.5,
      unite: "sac",
      taux_tva: 20,
      categorie: "Maçonnerie",
      actif: true,
    },
    {
      id: 2,
      entreprise_id: 1,
      nom: "Main d'œuvre qualifiée",
      type: "main d'œuvre",
      reference: "MO-001",
      description: "Travail d'un ouvrier qualifié",
      prix_unitaire_ht: 45,
      unite: "heure",
      taux_tva: 20,
      categorie: "Main d'œuvre",
      actif: true,
    },
  ],
  devis: [
    {
      id: 1,
      projet_id: 1,
      utilisateur_id: 1,
      reference: "DEV-2023-001",
      date_creation: new Date().toISOString(),
      date_validite: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
      statut: "brouillon",
      montant_ht: 1500,
      montant_ttc: 1800,
      validite_jours: 30,
      introduction: "Suite à votre demande, nous vous proposons...",
      conclusion: "En espérant que cette proposition réponde à vos attentes...",
      taux_remise: 0,
      conditions_acceptees: false,
      signature_url: null,
      date_signature: null,
    },
  ],
}

// Fonction pour obtenir des données de démonstration
export function getDemoData<T>(table: keyof typeof demoData): T[] {
  return demoData[table] as unknown as T[]
}

// Fonction pour vérifier si le mode démo est activé
export function isDemoMode(): boolean {
  return demoMode.enabled
}

// Fonction pour désactiver le mode démo (à utiliser en production)
export function disableDemoMode(): void {
  demoMode.enabled = false
}

// Fonction pour activer le mode démo (à utiliser en développement ou en cas d'erreur)
export function enableDemoMode(): void {
  demoMode.enabled = true
}
