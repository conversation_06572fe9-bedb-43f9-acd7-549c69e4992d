import { createClient } from "@supabase/supabase-js"
import { cookies } from "next/headers"
import { demoMode } from "./demo-mode"

// Singleton pour le client Supabase côté serveur
let serverSupabaseInstance = null

export function createServerSupabaseClient() {
  // Si nous sommes en mode démo, ne pas initialiser Supabase
  if (demoMode.enabled) {
    console.log("Mode démo activé, client Supabase serveur non initialisé")
    return null
  }

  // Si nous avons déjà une instance, la retourner
  if (serverSupabaseInstance) {
    return serverSupabaseInstance
  }

  const cookieStore = cookies()
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

  if (!supabaseUrl || !supabaseAnonKey) {
    console.warn("Variables d'environnement Supabase manquantes")
    return null
  }

  serverSupabaseInstance = createClient(supabaseUrl, supabase<PERSON>nonKey, {
    cookies: {
      get(name: string) {
        return cookieStore.get(name)?.value
      },
    },
  })

  return serverSupabaseInstance
}
