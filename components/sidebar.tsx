"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import {
  BarChart3,
  ClipboardList,
  FileText,
  Home,
  LayoutDashboard,
  Package,
  Settings,
  Users,
  MessageSquare,
} from "lucide-react"

import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"

export default function Sidebar() {
  const pathname = usePathname()

  const routes = [
    {
      label: "Tableau de bord",
      icon: LayoutDashboard,
      href: "/dashboard",
      active: pathname === "/dashboard",
    },
    {
      label: "Clients",
      icon: Users,
      href: "/clients",
      active: pathname === "/clients" || pathname.startsWith("/clients/"),
    },
    {
      label: "Demandes",
      icon: MessageSquare,
      href: "/demandes",
      active: pathname === "/demandes" || pathname.startsWith("/demandes/"),
    },
    {
      label: "Projets",
      icon: ClipboardList,
      href: "/projets",
      active: pathname === "/projets" || pathname.startsWith("/projets/"),
    },
    {
      label: "Devi<PERSON>",
      icon: FileText,
      href: "/devis",
      active: pathname === "/devis" || pathname.startsWith("/devis/"),
    },
    {
      label: "Factures",
      icon: FileText,
      href: "/factures",
      active: pathname === "/factures" || pathname.startsWith("/factures/"),
    },
    {
      label: "Catalogue",
      icon: Package,
      href: "/catalogue",
      active: pathname === "/catalogue" || pathname.startsWith("/catalogue/"),
    },
    {
      label: "Rapports",
      icon: BarChart3,
      href: "/rapports",
      active: pathname === "/rapports" || pathname.startsWith("/rapports/"),
    },
    {
      label: "Paramètres",
      icon: Settings,
      href: "/parameters",
      active: pathname === "/parameters" || pathname.startsWith("/parameters/"),
    },
  ]

  return (
    <div className="flex h-full w-64 flex-col border-r bg-background">
      <div className="flex h-14 items-center border-b px-4">
        <Link href="/dashboard" className="flex items-center gap-2 font-semibold">
          <Home className="h-5 w-5" />
          <span>BTP Manager</span>
        </Link>
      </div>
      <div className="flex-1 overflow-auto py-2">
        <nav className="grid gap-1 px-2">
          {routes.map((route) => (
            <Button
              key={route.href}
              variant={route.active ? "secondary" : "ghost"}
              className={cn("justify-start", route.active && "bg-secondary")}
              asChild
            >
              <Link href={route.href}>
                <route.icon className="mr-2 h-5 w-5" />
                {route.label}
              </Link>
            </Button>
          ))}
        </nav>
      </div>
    </div>
  )
}
