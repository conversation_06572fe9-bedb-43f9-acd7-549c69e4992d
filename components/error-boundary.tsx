"use client"

import { Component, type ErrorInfo, type ReactNode } from "react"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { AlertCircle, RefreshCw } from "lucide-react"

interface Props {
  children: ReactNode
  fallback?: ReactNode
}

interface State {
  hasError: boolean
  error: Error | null
  errorInfo: ErrorInfo | null
}

export class ErrorBoundary extends Component<Props, State> {
  public state: State = {
    hasError: false,
    error: null,
    errorInfo: null,
  }

  public static getDerivedStateFromError(error: Error): State {
    // Update state so the next render will show the fallback UI.
    return { hasError: true, error, errorInfo: null }
  }

  public componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error("Uncaught error:", error, errorInfo)
    this.setState({
      error,
      errorInfo,
    })
  }

  public render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback
      }

      return (
        <Card className="w-full max-w-3xl mx-auto my-8">
          <CardHeader>
            <CardTitle className="text-red-500">Une erreur est survenue</CardTitle>
            <CardDescription>
              Une erreur inattendue s&apos;est produite lors du chargement de cette page.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Alert variant="destructive" className="mb-4">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Erreur</AlertTitle>
              <AlertDescription className="font-mono text-sm">
                {this.state.error?.message || "Erreur inconnue"}
              </AlertDescription>
            </Alert>

            {this.state.error?.stack && (
              <div className="mt-4">
                <h4 className="text-sm font-medium mb-2">Détails techniques:</h4>
                <pre className="bg-slate-100 p-4 rounded text-xs overflow-auto max-h-40">{this.state.error.stack}</pre>
              </div>
            )}
          </CardContent>
          <CardFooter className="flex justify-between">
            <Button variant="outline" onClick={() => window.history.back()}>
              Retour
            </Button>
            <Button onClick={() => window.location.reload()} className="flex items-center">
              <RefreshCw className="mr-2 h-4 w-4" />
              Recharger la page
            </Button>
          </CardFooter>
        </Card>
      )
    }

    return this.props.children
  }
}
