"use client"

import { useEffect, useState } from "react"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Button } from "@/components/ui/button"
import { AlertTriangle } from "lucide-react"
import { connectionState, checkSupabaseConnection } from "@/lib/supabase"

export function OfflineWarning() {
  const [isOffline, setIsOffline] = useState(false)
  const [reason, setReason] = useState("")
  const [isChecking, setIsChecking] = useState(false)

  useEffect(() => {
    // Vérifier l'état de la connexion au chargement
    setIsOffline(connectionState.offlineMode.enabled)
    setReason(connectionState.offlineMode.reason)

    // Vérifier périodiquement la connexion
    const interval = setInterval(async () => {
      if (connectionState.offlineMode.enabled) {
        const { connected } = await checkSupabaseConnection()
        if (connected) {
          setIsOffline(false)
          setReason("")
        }
      }
    }, 30000) // Vérifier toutes les 30 secondes

    return () => clearInterval(interval)
  }, [])

  const handleRetry = async () => {
    setIsChecking(true)
    const { connected, error } = await checkSupabaseConnection()
    setIsOffline(!connected)
    setReason(error || "")
    setIsChecking(false)
  }

  if (!isOffline) {
    return null
  }

  return (
    <Alert variant="warning" className="mb-4">
      <AlertTriangle className="h-4 w-4" />
      <AlertTitle>Mode hors ligne</AlertTitle>
      <AlertDescription className="flex flex-col gap-2">
        <p>
          L'application fonctionne actuellement en mode hors ligne avec des données de démonstration. Certaines
          fonctionnalités peuvent être limitées.
        </p>
        <p className="text-sm text-muted-foreground">Raison : {reason}</p>
        <div>
          <Button variant="outline" size="sm" onClick={handleRetry} disabled={isChecking}>
            {isChecking ? "Vérification..." : "Vérifier la connexion"}
          </Button>
        </div>
      </AlertDescription>
    </Alert>
  )
}
