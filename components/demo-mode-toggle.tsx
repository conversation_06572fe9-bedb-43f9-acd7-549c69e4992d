"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { AlertCircle, Info } from "lucide-react"
import { isDemoMode, enableDemoMode, disableDemoMode } from "@/lib/demo-mode"
import { useToast } from "@/hooks/use-toast"

export function DemoModeToggle() {
  const [demoEnabled, setDemoEnabled] = useState(false)
  const { toast } = useToast()

  useEffect(() => {
    // Check current demo mode status
    setDemoEnabled(isDemoMode())
  }, [])

  const toggleDemoMode = () => {
    if (demoEnabled) {
      disableDemoMode()
      setDemoEnabled(false)
      toast({
        title: "Mode démo désactivé",
        description: "Vous utilisez maintenant les vraies données de Supabase",
      })
    } else {
      enableDemoMode()
      setDemoEnabled(true)
      toast({
        title: "Mode démo activé",
        description: "Vous utilisez maintenant des données de démonstration",
      })
    }

    // Force page refresh to apply changes
    window.location.reload()
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Mode Démonstration</CardTitle>
        <CardDescription>
          Activez ou désactivez le mode démonstration pour tester l'application sans utiliser de vraies données
        </CardDescription>
      </CardHeader>
      <CardContent>
        {demoEnabled && (
          <Alert variant="destructive" className="mb-4">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Mode démo actif</AlertTitle>
            <AlertDescription>
              Vous utilisez actuellement des données de démonstration. La connexion et les fonctionnalités réelles sont
              désactivées.
            </AlertDescription>
          </Alert>
        )}

        {!demoEnabled && (
          <Alert className="mb-4">
            <Info className="h-4 w-4" />
            <AlertTitle>Mode démo inactif</AlertTitle>
            <AlertDescription>
              Vous utilisez actuellement les vraies données de Supabase. Toutes les fonctionnalités sont actives.
            </AlertDescription>
          </Alert>
        )}

        <div className="flex items-center space-x-2">
          <Switch id="demo-mode" checked={demoEnabled} onCheckedChange={toggleDemoMode} />
          <Label htmlFor="demo-mode">{demoEnabled ? "Désactiver le mode démo" : "Activer le mode démo"}</Label>
        </div>
      </CardContent>
      <CardFooter>
        <Button variant={demoEnabled ? "destructive" : "outline"} onClick={toggleDemoMode} className="w-full">
          {demoEnabled ? "Désactiver le mode démo" : "Activer le mode démo"}
        </Button>
      </CardFooter>
    </Card>
  )
}
