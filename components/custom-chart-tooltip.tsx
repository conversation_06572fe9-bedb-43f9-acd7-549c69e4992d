"use client"
import { useContext } from "react"
import { ChartContext } from "@/components/ui/chart-context"

interface CustomTooltipProps {
  active?: boolean
  payload?: any[]
  label?: string
}

export function CustomChartTooltip({ active, payload, label }: CustomTooltipProps) {
  const config = useContext(ChartContext)

  if (!active || !payload?.length || !config) {
    return null
  }

  return (
    <div className="rounded-lg border bg-background p-2 shadow-sm">
      <div className="text-xs font-medium">{label}</div>
      <div className="mt-1 grid gap-0.5">
        {payload.map((item) => {
          const name = item.name || item.dataKey
          const value = item.value
          const { label: itemLabel, color } = config[name] || {
            label: name,
            color: "currentColor",
          }

          return (
            <div key={name} className="flex items-center gap-2">
              <div className="h-2 w-2 rounded-full" style={{ backgroundColor: color }} />
              <div className="text-xs font-medium">{itemLabel}</div>
              <div className="ml-auto text-xs font-medium">{value}</div>
            </div>
          )
        })}
      </div>
    </div>
  )
}
