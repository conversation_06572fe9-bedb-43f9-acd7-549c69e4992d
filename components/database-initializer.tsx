"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { AlertCircle, CheckCircle } from "lucide-react"

export function DatabaseInitializer() {
  const [isInitializing, setIsInitializing] = useState(false)
  const [result, setResult] = useState<{ success: boolean; message: string; error?: string } | null>(null)
  const [autoInit, setAutoInit] = useState(true)

  useEffect(() => {
    if (autoInit) {
      initDatabase()
      setAutoInit(false)
    }
  }, [autoInit])

  const initDatabase = async () => {
    setIsInitializing(true)
    try {
      const response = await fetch("/api/init-database")
      const data = await response.json()
      setResult(data)
    } catch (error: any) {
      setResult({
        success: false,
        message: "Une erreur s'est produite lors de l'initialisation de la base de données",
        error: error.message,
      })
    } finally {
      setIsInitializing(false)
    }
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Initialisation de la Base de Données</CardTitle>
        <CardDescription>
          Vérification et création des tables nécessaires pour le fonctionnement de l'application
        </CardDescription>
      </CardHeader>
      <CardContent>
        {isInitializing ? (
          <div className="flex items-center justify-center p-4">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            <span className="ml-2">Initialisation en cours...</span>
          </div>
        ) : result ? (
          <Alert variant={result.success ? "default" : "destructive"}>
            {result.success ? <CheckCircle className="h-4 w-4" /> : <AlertCircle className="h-4 w-4" />}
            <AlertTitle>{result.success ? "Succès" : "Erreur"}</AlertTitle>
            <AlertDescription>
              {result.message}
              {result.error && <p className="text-sm mt-2">{result.error}</p>}
            </AlertDescription>
          </Alert>
        ) : null}
      </CardContent>
      <CardFooter>
        <Button onClick={initDatabase} disabled={isInitializing}>
          {isInitializing ? "Initialisation en cours..." : "Réinitialiser la base de données"}
        </Button>
      </CardFooter>
    </Card>
  )
}
