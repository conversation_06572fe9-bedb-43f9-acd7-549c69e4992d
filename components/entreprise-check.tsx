"use client"

import type React from "react"

import { useEffect, useState } from "react"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { getEntreprises } from "@/services/entreprises"
import Link from "next/link"
import { isDemoMode } from "@/lib/demo-mode"

export function EntrepriseCheck({ children }: { children: React.ReactNode }) {
  const [isChecking, setIsChecking] = useState(true)
  const [hasEntreprise, setHasEntreprise] = useState(true)

  useEffect(() => {
    async function checkEntreprise() {
      try {
        // En mode démo, on considère toujours qu'il y a une entreprise
        if (isDemoMode()) {
          setHasEntreprise(true)
          setIsChecking(false)
          return
        }

        const entreprises = await getEntreprises()
        setHasEntreprise(entreprises.length > 0)
      } catch (err) {
        console.error("Erreur lors de la vérification des entreprises:", err)
        // En cas d'erreur, on considère qu'il y a une entreprise pour ne pas bloquer l'application
        setHasEntreprise(true)
      } finally {
        setIsChecking(false)
      }
    }

    checkEntreprise()
  }, [])

  if (isChecking) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Vérification de la configuration...</p>
        </div>
      </div>
    )
  }

  if (!hasEntreprise) {
    return (
      <Card className="w-full max-w-md mx-auto mt-8">
        <CardHeader>
          <CardTitle>Configuration requise</CardTitle>
          <CardDescription>Vous devez configurer votre entreprise pour continuer</CardDescription>
        </CardHeader>
        <CardContent>
          <p>Avant de pouvoir utiliser l'application, vous devez configurer les informations de votre entreprise.</p>
        </CardContent>
        <CardFooter>
          <Button asChild className="w-full">
            <Link href="/admin/entreprise">Configurer mon entreprise</Link>
          </Button>
        </CardFooter>
      </Card>
    )
  }

  // Render children properly
  return <>{typeof children === "function" ? children() : children}</>
}
