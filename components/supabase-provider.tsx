"use client"

import type React from "react"

import { useEffect, useState } from "react"
import { checkSupabaseConnection } from "@/lib/supabase"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { AlertCircle } from "lucide-react"
import { Button } from "@/components/ui/button"
import { isDemoMode } from "@/lib/demo-mode"

export function SupabaseProvider({ children }: { children: React.ReactNode }) {
  const [isChecking, setIsChecking] = useState(true)
  const [connectionError, setConnectionError] = useState<string | null>(null)

  useEffect(() => {
    async function verifyConnection() {
      try {
        const { connected, error } = await checkSupabaseConnection()
        if (!connected) {
          setConnectionError(error || "Impossible de se connecter à la base de données")
        } else {
          setConnectionError(null)
        }
      } catch (err) {
        setConnectionError("Erreur lors de la vérification de la connexion")
      } finally {
        setIsChecking(false)
      }
    }

    verifyConnection()
  }, [])

  const retryConnection = async () => {
    setIsChecking(true)
    setConnectionError(null)
    try {
      const { connected, error } = await checkSupabaseConnection()
      if (!connected) {
        setConnectionError(error || "Impossible de se connecter à la base de données")
      } else {
        setConnectionError(null)
      }
    } catch (err) {
      setConnectionError("Erreur lors de la vérification de la connexion")
    } finally {
      setIsChecking(false)
    }
  }

  if (isChecking) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Connexion à la base de données...</p>
        </div>
      </div>
    )
  }

  if (connectionError && isDemoMode()) {
    return (
      <>
        <Alert variant="destructive" className="mb-4">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Erreur de connexion à la base de données</AlertTitle>
          <AlertDescription className="flex flex-col gap-2">
            <p>{connectionError}</p>
            <p className="text-sm">L'application fonctionne en mode démonstration avec des données fictives.</p>
            <div>
              <Button variant="outline" size="sm" onClick={retryConnection}>
                Réessayer la connexion
              </Button>
            </div>
          </AlertDescription>
        </Alert>
        {typeof children === "function" ? children() : children}
      </>
    )
  }

  return <>{typeof children === "function" ? children() : children}</>
}
