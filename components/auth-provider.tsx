"use client"

import type React from "react"

import { createContext, useContext, useEffect, useState } from "react"
import { useRouter, usePathname } from "next/navigation"
import { supabase } from "@/lib/supabase"
import type { Session, User } from "@supabase/supabase-js"

type AuthContextType = {
  user: User | null
  session: Session | null
  isLoading: boolean
}

const AuthContext = createContext<AuthContextType>({
  user: null,
  session: null,
  isLoading: true,
})

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (!context) {
    // Return default values if context is not available (during SSR)
    return {
      user: null,
      session: null,
      isLoading: true,
    }
  }
  return context
}

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [session, setSession] = useState<Session | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [mounted, setMounted] = useState(false)
  const router = useRouter()
  const pathname = usePathname()

  // Prevent SSR issues
  useEffect(() => {
    setMounted(true)
  }, [])

  useEffect(() => {
    // Only run auth logic after component is mounted
    if (!mounted) return

    // Check if Supabase client is available
    if (!supabase) {
      console.error("Supabase client not available in AuthProvider")
      setIsLoading(false)
      return
    }

    // Get session on initial load
    const getInitialSession = async () => {
      try {
        const { data, error } = await supabase.auth.getSession()

        if (error) {
          console.error("AuthProvider: Error getting session:", error)
          setSession(null)
          setUser(null)
        } else {
          // Validate session is actually valid
          if (data.session && data.session.user) {
            setSession(data.session)
            setUser(data.session.user)

            // If user is logged in and on login/register page, redirect to dashboard
            if (pathname === "/login" || pathname === "/register") {
              router.push("/dashboard")
            }
          } else {
            setSession(null)
            setUser(null)
          }
        }
      } catch (error) {
        console.error("AuthProvider: Unexpected error during getSession:", error)
        setSession(null)
        setUser(null)
      } finally {
        setIsLoading(false)
      }
    }

    getInitialSession()

    // Set up auth state listener
    const { data: authListener } = supabase.auth.onAuthStateChange(async (event, newSession) => {
      setSession(newSession)
      setUser(newSession?.user ?? null)

      // Handle auth state changes
      if (event === "SIGNED_IN" && newSession) {
        if (pathname === "/login" || pathname === "/register") {
          router.push("/dashboard")
        }
      } else if (event === "SIGNED_OUT") {
        router.push("/login")
      }

      // Force a router refresh to update server components
      router.refresh()
    })

    // Clean up subscription
    return () => {
      authListener.subscription.unsubscribe()
    }
  }, [router, pathname, mounted])

  return <AuthContext.Provider value={{ user, session, isLoading }}>{children}</AuthContext.Provider>
}
