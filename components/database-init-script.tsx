"use client"

import { useEffect } from "react"

export function DatabaseInitScript() {
  useEffect(() => {
    // Initialize database on startup
    fetch("/api/init-database")
      .then((response) => response.json())
      .then((data) => {
        if (data.success) {
          console.log("Base de données initialisée avec succès")
        } else {
          console.error("Erreur lors de l'initialisation de la base de données:", data.error)
        }
      })
      .catch((error) => {
        console.error("Erreur lors de l'initialisation de la base de données:", error)
      })
  }, [])

  return null
}
