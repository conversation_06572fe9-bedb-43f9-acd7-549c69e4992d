"use client"

import { useEffect, useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Download, FileText, RefreshCw, Edit, Plus } from "lucide-react"
import { type Devis, getDevis } from "@/services/devis"
import { useToast } from "@/hooks/use-toast"
import Link from "next/link"

export function DevisList() {
  const [devis, setDevis] = useState<Devis[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [debugInfo, setDebugInfo] = useState<string>("")
  const { toast } = useToast()

  async function loadDevis() {
    setIsLoading(true)
    setDebugInfo("Chargement en cours...")

    try {
      console.log("🚀 Starting to load devis...")
      const data = await getDevis()
      console.log("📊 Received data:", data)

      setDevis(data)
      setDebugInfo(`✅ ${data.length} devis chargés avec succès`)

      if (data.length === 0) {
        toast({
          title: "Information",
          description: "Aucun devis trouvé dans la base de données",
        })
      }
    } catch (error: any) {
      console.error("💥 Error loading devis:", error)
      setDebugInfo(`❌ Erreur: ${error.message}`)
      toast({
        title: "Erreur",
        description: error.message || "Impossible de charger les devis",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    loadDevis()
  }, [])

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div>
          <p>Chargement des devis...</p>
          <p className="text-sm text-muted-foreground mt-2">{debugInfo}</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="text-sm text-muted-foreground">{debugInfo}</div>
        <div className="flex gap-2">
          <Link href="/devis/nouveau">
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Nouveau devis
            </Button>
          </Link>
          <Button onClick={loadDevis} variant="outline" size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            Actualiser
          </Button>
        </div>
      </div>

      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>ID</TableHead>
            <TableHead>Référence</TableHead>
            <TableHead>Date</TableHead>
            <TableHead>Validité</TableHead>
            <TableHead>Montant HT</TableHead>
            <TableHead>Montant TTC</TableHead>
            <TableHead>Statut</TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {devis.length === 0 ? (
            <TableRow>
              <TableCell colSpan={8} className="text-center py-8">
                <div className="text-muted-foreground">
                  <p>Aucun devis trouvé</p>
                  <p className="text-sm mt-1">
                    Vérifiez que des devis existent dans la table et que vous avez les permissions nécessaires
                  </p>
                  <Link href="/devis/nouveau">
                    <Button className="mt-4">
                      <Plus className="h-4 w-4 mr-2" />
                      Créer le premier devis
                    </Button>
                  </Link>
                </div>
              </TableCell>
            </TableRow>
          ) : (
            devis.map((d) => (
              <TableRow key={d.id}>
                <TableCell className="font-mono text-sm">{d.id}</TableCell>
                <TableCell className="font-medium">{d.reference || "N/A"}</TableCell>
                <TableCell>{d.date_creation ? new Date(d.date_creation).toLocaleDateString("fr-FR") : "N/A"}</TableCell>
                <TableCell>
                  {d.date_validite ? new Date(d.date_validite).toLocaleDateString("fr-FR") : "Non définie"}
                </TableCell>
                <TableCell>{d.montant_ht?.toLocaleString("fr-FR") || "0"} €</TableCell>
                <TableCell>{d.montant_ttc?.toLocaleString("fr-FR") || "0"} €</TableCell>
                <TableCell>
                  <Badge
                    variant={
                      d.statut === "signé"
                        ? "success"
                        : d.statut === "envoyé"
                          ? "default"
                          : d.statut === "refusé"
                            ? "destructive"
                            : "outline"
                    }
                  >
                    {d.statut || "brouillon"}
                  </Badge>
                </TableCell>
                <TableCell className="text-right">
                  <div className="flex justify-end gap-1">
                    <Link href={`/devis/${d.id}`}>
                      <Button variant="ghost" size="icon">
                        <FileText className="h-4 w-4" />
                        <span className="sr-only">Voir le devis</span>
                      </Button>
                    </Link>
                    <Link href={`/devis/${d.id}/edit`}>
                      <Button variant="ghost" size="icon">
                        <Edit className="h-4 w-4" />
                        <span className="sr-only">Modifier le devis</span>
                      </Button>
                    </Link>
                    <Button variant="ghost" size="icon">
                      <Download className="h-4 w-4" />
                      <span className="sr-only">Télécharger</span>
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))
          )}
        </TableBody>
      </Table>
    </div>
  )
}
