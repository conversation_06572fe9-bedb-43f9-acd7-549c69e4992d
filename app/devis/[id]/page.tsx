import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { ArrowLeft, Download, Edit, Eye, Mail, Printer } from "lucide-react"
import Link from "next/link"
import { notFound } from "next/navigation"
import { getDevisById } from "@/services/devis"
import { formatDate, formatCurrency } from "@/lib/utils"

interface DevisDetailPageProps {
  params: {
    id: string
  }
}

export default async function DevisDetailPage({ params }: DevisDetailPageProps) {
  const devisId = Number.parseInt(params.id)

  if (isNaN(devisId)) {
    notFound()
  }

  const devis = await getDevisById(devisId)

  if (!devis) {
    notFound()
  }

  const getStatusBadge = (statut: string) => {
    switch (statut) {
      case "brouillon":
        return <Badge variant="secondary">Brouillon</Badge>
      case "envoyé":
        return <Badge variant="default">Envoyé</Badge>
      case "signé":
        return (
          <Badge variant="default" className="bg-green-500">
            Signé
          </Badge>
        )
      case "refusé":
        return <Badge variant="destructive">Refusé</Badge>
      default:
        return <Badge variant="outline">{statut}</Badge>
    }
  }

  // Calculate totals from sections and lines
  const calculateTotals = () => {
    if (!devis.sections || devis.sections.length === 0) {
      return {
        totalHT: devis.montant_ht || 0,
        totalTTC: devis.montant_ttc || 0,
        totalTVA: (devis.montant_ttc || 0) - (devis.montant_ht || 0),
      }
    }

    const totalHT = devis.sections.reduce((sectionTotal, section) => {
      return (
        sectionTotal +
        (section.lignes?.reduce((ligneTotal, ligne) => {
          return ligneTotal + ligne.quantite * ligne.prix_unitaire_ht
        }, 0) || 0)
      )
    }, 0)

    const totalTVA = devis.sections.reduce((sectionTotal, section) => {
      return (
        sectionTotal +
        (section.lignes?.reduce((ligneTotal, ligne) => {
          const montantHT = ligne.quantite * ligne.prix_unitaire_ht
          return ligneTotal + (montantHT * ligne.taux_tva) / 100
        }, 0) || 0)
      )
    }, 0)

    return {
      totalHT,
      totalTVA,
      totalTTC: totalHT + totalTVA,
    }
  }

  const { totalHT, totalTVA, totalTTC } = calculateTotals()

  return (
    <div className="flex flex-col gap-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Link href="/devis">
            <Button variant="outline" size="sm">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Retour
            </Button>
          </Link>
          <div>
            <h1 className="text-2xl font-bold">Devis #{devis.reference}</h1>
            <p className="text-muted-foreground">Créé le {formatDate(devis.date_creation)}</p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          {getStatusBadge(devis.statut)}
          <Link href={`/devis/${devis.id}/edit`}>
            <Button variant="outline" size="sm">
              <Edit className="mr-2 h-4 w-4" />
              Modifier
            </Button>
          </Link>
          <Button variant="outline" size="sm">
            <Eye className="mr-2 h-4 w-4" />
            Aperçu
          </Button>
          <Button variant="outline" size="sm">
            <Mail className="mr-2 h-4 w-4" />
            Envoyer
          </Button>
          <Button variant="outline" size="sm">
            <Download className="mr-2 h-4 w-4" />
            PDF
          </Button>
          <Button variant="outline" size="sm">
            <Printer className="mr-2 h-4 w-4" />
            Imprimer
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Devis Information */}
          <Card>
            <CardHeader>
              <CardTitle>Informations du devis</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm font-medium">Référence</p>
                  <p className="text-sm text-muted-foreground">{devis.reference}</p>
                </div>
                <div>
                  <p className="text-sm font-medium">Statut</p>
                  <div className="mt-1">{getStatusBadge(devis.statut)}</div>
                </div>
                <div>
                  <p className="text-sm font-medium">Date de création</p>
                  <p className="text-sm text-muted-foreground">{formatDate(devis.date_creation)}</p>
                </div>
                <div>
                  <p className="text-sm font-medium">Date de validité</p>
                  <p className="text-sm text-muted-foreground">
                    {devis.date_validite ? formatDate(devis.date_validite) : "Non définie"}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Content */}
          {(devis.introduction || devis.conclusion) && (
            <Card>
              <CardHeader>
                <CardTitle>Contenu du devis</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {devis.introduction && (
                  <div>
                    <h3 className="font-semibold mb-2">Introduction</h3>
                    <p className="text-sm text-muted-foreground">{devis.introduction}</p>
                  </div>
                )}
                {devis.conclusion && (
                  <div>
                    <h3 className="font-semibold mb-2">Conclusion</h3>
                    <p className="text-sm text-muted-foreground">{devis.conclusion}</p>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Sections and Lines */}
          {devis.sections && devis.sections.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Détail du devis</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-8">
                  {devis.sections.map((section, sectionIndex) => (
                    <div key={section.id} className="space-y-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <h3 className="text-lg font-semibold">{section.titre}</h3>
                          {section.description && (
                            <p className="text-sm text-muted-foreground mt-1">{section.description}</p>
                          )}
                        </div>
                        <Badge variant="outline">{section.lignes?.length || 0} ligne(s)</Badge>
                      </div>

                      {section.lignes && section.lignes.length > 0 && (
                        <Table>
                          <TableHeader>
                            <TableRow>
                              <TableHead>Désignation</TableHead>
                              <TableHead className="text-right">Qté</TableHead>
                              <TableHead className="text-right">Unité</TableHead>
                              <TableHead className="text-right">Prix unit. HT</TableHead>
                              <TableHead className="text-right">TVA</TableHead>
                              <TableHead className="text-right">Total HT</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {section.lignes.map((ligne) => (
                              <TableRow key={ligne.id}>
                                <TableCell>
                                  <div>
                                    <p className="font-medium">{ligne.designation}</p>
                                    {ligne.description && (
                                      <p className="text-sm text-muted-foreground">{ligne.description}</p>
                                    )}
                                    {ligne.reference && (
                                      <p className="text-xs text-muted-foreground">Réf: {ligne.reference}</p>
                                    )}
                                  </div>
                                </TableCell>
                                <TableCell className="text-right">{ligne.quantite}</TableCell>
                                <TableCell className="text-right">{ligne.unite}</TableCell>
                                <TableCell className="text-right">{formatCurrency(ligne.prix_unitaire_ht)}</TableCell>
                                <TableCell className="text-right">{ligne.taux_tva}%</TableCell>
                                <TableCell className="text-right font-medium">
                                  {formatCurrency(ligne.quantite * ligne.prix_unitaire_ht)}
                                </TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      )}

                      {sectionIndex < devis.sections.length - 1 && <Separator className="my-6" />}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Empty state for sections */}
          {(!devis.sections || devis.sections.length === 0) && (
            <Card>
              <CardHeader>
                <CardTitle>Détail du devis</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8">
                  <p className="text-muted-foreground mb-4">Aucune section n'a été ajoutée à ce devis.</p>
                  <Link href={`/devis/${devis.id}/edit`}>
                    <Button>Ajouter des sections</Button>
                  </Link>
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Summary */}
          <Card>
            <CardHeader>
              <CardTitle>Résumé financier</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex justify-between">
                <span>Total HT</span>
                <span className="font-medium">{formatCurrency(totalHT)}</span>
              </div>
              <div className="flex justify-between">
                <span>TVA</span>
                <span className="font-medium">{formatCurrency(totalTVA)}</span>
              </div>
              {devis.taux_remise > 0 && (
                <div className="flex justify-between text-red-600">
                  <span>Remise ({devis.taux_remise}%)</span>
                  <span className="font-medium">-{formatCurrency((totalHT * devis.taux_remise) / 100)}</span>
                </div>
              )}
              <Separator />
              <div className="flex justify-between text-lg font-bold">
                <span>Total TTC</span>
                <span>{formatCurrency(totalTTC)}</span>
              </div>
            </CardContent>
          </Card>

          {/* Details */}
          <Card>
            <CardHeader>
              <CardTitle>Détails</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <p className="text-sm font-medium">Projet ID</p>
                <p className="text-sm text-muted-foreground">{devis.projet_id}</p>
              </div>
              <div>
                <p className="text-sm font-medium">Utilisateur ID</p>
                <p className="text-sm text-muted-foreground">{devis.utilisateur_id}</p>
              </div>
              <div>
                <p className="text-sm font-medium">Validité</p>
                <p className="text-sm text-muted-foreground">{devis.validite_jours} jours</p>
              </div>
              <div>
                <p className="text-sm font-medium">Sections</p>
                <p className="text-sm text-muted-foreground">{devis.sections?.length || 0}</p>
              </div>
              <div>
                <p className="text-sm font-medium">Lignes</p>
                <p className="text-sm text-muted-foreground">
                  {devis.sections?.reduce((total, section) => total + (section.lignes?.length || 0), 0) || 0}
                </p>
              </div>
              {devis.date_signature && (
                <div>
                  <p className="text-sm font-medium">Date de signature</p>
                  <p className="text-sm text-muted-foreground">{formatDate(devis.date_signature)}</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Actions rapides</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <Button className="w-full" variant="default">
                Convertir en facture
              </Button>
              <Button className="w-full" variant="outline">
                Dupliquer le devis
              </Button>
              <Button className="w-full" variant="outline">
                Créer un projet
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
