"use client"

import type React from "react"

import { useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Calendar } from "@/components/ui/calendar"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { fr } from "date-fns/locale"
import { format } from "date-fns"
import { CalendarIcon, ArrowLeft, Save, Plus, Trash2, Ch<PERSON>ronUp, ChevronDown } from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import {
  getDevisById,
  updateDevis,
  createSectionDevis,
  updateSectionDevis,
  deleteSectionDevis,
  createLigneDevis,
  updateLigneDevis,
  deleteLigneDevis,
  type Devis,
  type SectionDevis,
  type LigneDevis,
} from "@/services/devis"
import Link from "next/link"

interface EditDevisPageProps {
  params: {
    id: string
  }
}

export default function EditDevisPage({ params }: EditDevisPageProps) {
  const router = useRouter()
  const { toast } = useToast()
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const [devis, setDevis] = useState<Devis | null>(null)
  const [sections, setSections] = useState<SectionDevis[]>([])
  const [formData, setFormData] = useState({
    reference: "",
    date_validite: new Date(),
    statut: "brouillon" as const,
    introduction: "",
    conclusion: "",
    montant_ht: 0,
    montant_ttc: 0,
    taux_remise: 0,
    validite_jours: 30,
    conditions_acceptees: false,
  })

  const devisId = Number.parseInt(params.id)

  useEffect(() => {
    async function loadDevis() {
      if (isNaN(devisId)) {
        toast({
          title: "Erreur",
          description: "ID de devis invalide",
          variant: "destructive",
        })
        router.push("/devis")
        return
      }

      try {
        const data = await getDevisById(devisId)
        setDevis(data)
        setSections(data.sections || [])
        setFormData({
          reference: data.reference || "",
          date_validite: data.date_validite ? new Date(data.date_validite) : new Date(),
          statut: data.statut || "brouillon",
          introduction: data.introduction || "",
          conclusion: data.conclusion || "",
          montant_ht: data.montant_ht || 0,
          montant_ttc: data.montant_ttc || 0,
          taux_remise: data.taux_remise || 0,
          validite_jours: data.validite_jours || 30,
          conditions_acceptees: data.conditions_acceptees || false,
        })
      } catch (error: any) {
        toast({
          title: "Erreur",
          description: error.message || "Impossible de charger le devis",
          variant: "destructive",
        })
        router.push("/devis")
      } finally {
        setIsLoading(false)
      }
    }

    loadDevis()
  }, [devisId, router, toast])

  const calculateTotals = () => {
    const totalHT = sections.reduce((sectionTotal, section) => {
      return (
        sectionTotal +
        (section.lignes?.reduce((ligneTotal, ligne) => {
          return ligneTotal + ligne.quantite * ligne.prix_unitaire_ht
        }, 0) || 0)
      )
    }, 0)

    const totalTVA = sections.reduce((sectionTotal, section) => {
      return (
        sectionTotal +
        (section.lignes?.reduce((ligneTotal, ligne) => {
          const montantHT = ligne.quantite * ligne.prix_unitaire_ht
          return ligneTotal + (montantHT * ligne.taux_tva) / 100
        }, 0) || 0)
      )
    }, 0)

    return {
      totalHT,
      totalTVA,
      totalTTC: totalHT + totalTVA,
    }
  }

  const { totalHT, totalTTC } = calculateTotals()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSaving(true)

    try {
      await updateDevis(devisId, {
        reference: formData.reference,
        date_validite: formData.date_validite.toISOString(),
        statut: formData.statut,
        introduction: formData.introduction,
        conclusion: formData.conclusion,
        montant_ht: totalHT,
        montant_ttc: totalTTC,
        taux_remise: formData.taux_remise,
        validite_jours: formData.validite_jours,
        conditions_acceptees: formData.conditions_acceptees,
      })

      toast({
        title: "Succès",
        description: "Devis mis à jour avec succès",
      })

      router.push(`/devis/${devisId}`)
    } catch (error: any) {
      toast({
        title: "Erreur",
        description: error.message || "Impossible de mettre à jour le devis",
        variant: "destructive",
      })
    } finally {
      setIsSaving(false)
    }
  }

  const addSection = async () => {
    try {
      const newSection = await createSectionDevis({
        devis_id: devisId,
        titre: "Nouvelle section",
        description: "",
        ordre: sections.length + 1,
        montant_ht: 0,
      })

      setSections([...sections, { ...newSection, lignes: [] }])
    } catch (error: any) {
      toast({
        title: "Erreur",
        description: error.message || "Impossible d'ajouter la section",
        variant: "destructive",
      })
    }
  }

  const updateSection = async (sectionId: number, updates: Partial<SectionDevis>) => {
    try {
      await updateSectionDevis(sectionId, updates)
      setSections(sections.map((section) => (section.id === sectionId ? { ...section, ...updates } : section)))
    } catch (error: any) {
      toast({
        title: "Erreur",
        description: error.message || "Impossible de mettre à jour la section",
        variant: "destructive",
      })
    }
  }

  const deleteSection = async (sectionId: number) => {
    try {
      await deleteSectionDevis(sectionId)
      setSections(sections.filter((section) => section.id !== sectionId))
    } catch (error: any) {
      toast({
        title: "Erreur",
        description: error.message || "Impossible de supprimer la section",
        variant: "destructive",
      })
    }
  }

  const addLigne = async (sectionId: number) => {
    try {
      const newLigne = await createLigneDevis({
        section_id: sectionId,
        catalogue_item_id: null,
        designation: "Nouvelle ligne",
        reference: "",
        description: "",
        quantite: 1,
        unite: "unité",
        prix_unitaire_ht: 0,
        taux_tva: 20,
        montant_ht: 0,
        montant_ttc: 0,
        ordre: 1,
      })

      setSections(
        sections.map((section) => {
          if (section.id === sectionId) {
            return {
              ...section,
              lignes: [...(section.lignes || []), newLigne],
            }
          }
          return section
        }),
      )
    } catch (error: any) {
      toast({
        title: "Erreur",
        description: error.message || "Impossible d'ajouter la ligne",
        variant: "destructive",
      })
    }
  }

  const updateLigne = async (ligneId: number, updates: Partial<LigneDevis>) => {
    try {
      await updateLigneDevis(ligneId, updates)
      setSections(
        sections.map((section) => ({
          ...section,
          lignes: section.lignes?.map((ligne) => (ligne.id === ligneId ? { ...ligne, ...updates } : ligne)),
        })),
      )
    } catch (error: any) {
      toast({
        title: "Erreur",
        description: error.message || "Impossible de mettre à jour la ligne",
        variant: "destructive",
      })
    }
  }

  const deleteLigne = async (ligneId: number) => {
    try {
      await deleteLigneDevis(ligneId)
      setSections(
        sections.map((section) => ({
          ...section,
          lignes: section.lignes?.filter((ligne) => ligne.id !== ligneId),
        })),
      )
    } catch (error: any) {
      toast({
        title: "Erreur",
        description: error.message || "Impossible de supprimer la ligne",
        variant: "destructive",
      })
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div>
          <p>Chargement du devis...</p>
        </div>
      </div>
    )
  }

  if (!devis) {
    return (
      <div className="text-center p-8">
        <p>Devis non trouvé</p>
        <Link href="/devis">
          <Button className="mt-4">Retour à la liste</Button>
        </Link>
      </div>
    )
  }

  return (
    <div className="flex flex-col gap-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Link href={`/devis/${devisId}`}>
            <Button variant="outline" size="sm">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Retour
            </Button>
          </Link>
          <div>
            <h1 className="text-2xl font-bold">Modifier le devis #{devis.reference}</h1>
            <p className="text-muted-foreground">Modifiez les informations du devis</p>
          </div>
        </div>
      </div>

      <form onSubmit={handleSubmit}>
        <div className="grid gap-6 md:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle>Informations générales</CardTitle>
              <CardDescription>Informations de base du devis</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="reference">Référence</Label>
                <Input
                  id="reference"
                  value={formData.reference}
                  onChange={(e) => setFormData({ ...formData, reference: e.target.value })}
                  placeholder="DEV-2023-043"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="date_validite">Date de validité</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button variant="outline" className="w-full justify-start text-left font-normal">
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {formData.date_validite
                        ? format(formData.date_validite, "PPP", { locale: fr })
                        : "Sélectionner une date"}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={formData.date_validite}
                      onSelect={(date) => date && setFormData({ ...formData, date_validite: date })}
                      locale={fr}
                    />
                  </PopoverContent>
                </Popover>
              </div>

              <div className="space-y-2">
                <Label htmlFor="statut">Statut</Label>
                <Select
                  value={formData.statut}
                  onValueChange={(value) => setFormData({ ...formData, statut: value as any })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="brouillon">Brouillon</SelectItem>
                    <SelectItem value="envoyé">Envoyé</SelectItem>
                    <SelectItem value="signé">Signé</SelectItem>
                    <SelectItem value="refusé">Refusé</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="validite_jours">Validité (jours)</Label>
                <Input
                  id="validite_jours"
                  type="number"
                  value={formData.validite_jours}
                  onChange={(e) => setFormData({ ...formData, validite_jours: Number.parseInt(e.target.value) || 30 })}
                  min="1"
                  max="365"
                />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Résumé financier</CardTitle>
              <CardDescription>Calculé automatiquement à partir des lignes</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label>Total HT (calculé)</Label>
                <div className="text-lg font-semibold">{totalHT.toFixed(2)} €</div>
              </div>

              <div className="space-y-2">
                <Label>Total TTC (calculé)</Label>
                <div className="text-lg font-semibold">{totalTTC.toFixed(2)} €</div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="taux_remise">Taux de remise (%)</Label>
                <Input
                  id="taux_remise"
                  type="number"
                  step="0.01"
                  value={formData.taux_remise}
                  onChange={(e) => setFormData({ ...formData, taux_remise: Number.parseFloat(e.target.value) || 0 })}
                  min="0"
                  max="100"
                />
              </div>
            </CardContent>
          </Card>

          <Card className="md:col-span-2">
            <CardHeader>
              <CardTitle>Contenu</CardTitle>
              <CardDescription>Introduction et conclusion du devis</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="introduction">Introduction</Label>
                <Textarea
                  id="introduction"
                  value={formData.introduction}
                  onChange={(e) => setFormData({ ...formData, introduction: e.target.value })}
                  placeholder="Suite à votre demande, nous vous proposons..."
                  rows={3}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="conclusion">Conclusion</Label>
                <Textarea
                  id="conclusion"
                  value={formData.conclusion}
                  onChange={(e) => setFormData({ ...formData, conclusion: e.target.value })}
                  placeholder="En espérant que cette proposition réponde à vos attentes..."
                  rows={3}
                />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Sections Management */}
        <Card className="mt-6">
          <CardHeader className="flex flex-row items-center justify-between">
            <div>
              <CardTitle>Sections du devis</CardTitle>
              <CardDescription>Gérez les sections et lignes de votre devis</CardDescription>
            </div>
            <Button type="button" onClick={addSection}>
              <Plus className="mr-2 h-4 w-4" />
              Ajouter une section
            </Button>
          </CardHeader>
          <CardContent className="space-y-6">
            {sections.map((section, sectionIndex) => (
              <div key={section.id} className="rounded-md border p-4">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-2">
                    <div className="flex flex-col items-center">
                      <Button variant="ghost" size="icon" className="h-6 w-6">
                        <ChevronUp className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="icon" className="h-6 w-6">
                        <ChevronDown className="h-4 w-4" />
                      </Button>
                    </div>
                    <div className="font-medium">Section {sectionIndex + 1}</div>
                  </div>
                  <Button type="button" variant="ghost" size="icon" onClick={() => deleteSection(section.id)}>
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>

                <div className="grid gap-4 mb-4">
                  <div className="space-y-2">
                    <Label>Titre</Label>
                    <Input
                      value={section.titre}
                      onChange={(e) => updateSection(section.id, { titre: e.target.value })}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label>Description</Label>
                    <Textarea
                      value={section.description}
                      onChange={(e) => updateSection(section.id, { description: e.target.value })}
                    />
                  </div>
                </div>

                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-[300px]">Désignation</TableHead>
                      <TableHead>Quantité</TableHead>
                      <TableHead>Unité</TableHead>
                      <TableHead>Prix unitaire HT</TableHead>
                      <TableHead>TVA</TableHead>
                      <TableHead>Total HT</TableHead>
                      <TableHead></TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {section.lignes?.map((ligne) => (
                      <TableRow key={ligne.id}>
                        <TableCell>
                          <Input
                            value={ligne.designation}
                            onChange={(e) => updateLigne(ligne.id, { designation: e.target.value })}
                          />
                        </TableCell>
                        <TableCell>
                          <Input
                            type="number"
                            value={ligne.quantite}
                            onChange={(e) =>
                              updateLigne(ligne.id, { quantite: Number.parseFloat(e.target.value) || 0 })
                            }
                          />
                        </TableCell>
                        <TableCell>
                          <Select
                            value={ligne.unite}
                            onValueChange={(value) => updateLigne(ligne.id, { unite: value })}
                          >
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="unité">Unité</SelectItem>
                              <SelectItem value="m²">m²</SelectItem>
                              <SelectItem value="m">m</SelectItem>
                              <SelectItem value="forfait">Forfait</SelectItem>
                              <SelectItem value="heure">Heure</SelectItem>
                            </SelectContent>
                          </Select>
                        </TableCell>
                        <TableCell>
                          <Input
                            type="number"
                            step="0.01"
                            value={ligne.prix_unitaire_ht}
                            onChange={(e) =>
                              updateLigne(ligne.id, { prix_unitaire_ht: Number.parseFloat(e.target.value) || 0 })
                            }
                          />
                        </TableCell>
                        <TableCell>
                          <Select
                            value={ligne.taux_tva.toString()}
                            onValueChange={(value) => updateLigne(ligne.id, { taux_tva: Number.parseFloat(value) })}
                          >
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="20">20%</SelectItem>
                              <SelectItem value="10">10%</SelectItem>
                              <SelectItem value="5.5">5.5%</SelectItem>
                              <SelectItem value="0">0%</SelectItem>
                            </SelectContent>
                          </Select>
                        </TableCell>
                        <TableCell className="font-medium">
                          {(ligne.quantite * ligne.prix_unitaire_ht).toFixed(2)} €
                        </TableCell>
                        <TableCell>
                          <Button type="button" variant="ghost" size="icon" onClick={() => deleteLigne(ligne.id)}>
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>

                <div className="mt-4">
                  <Button type="button" variant="outline" onClick={() => addLigne(section.id)}>
                    <Plus className="mr-2 h-4 w-4" />
                    Ajouter une ligne
                  </Button>
                </div>
              </div>
            ))}

            {sections.length === 0 && (
              <div className="text-center py-8">
                <p className="text-muted-foreground mb-4">Aucune section n'a été ajoutée.</p>
                <Button type="button" onClick={addSection}>
                  <Plus className="mr-2 h-4 w-4" />
                  Ajouter la première section
                </Button>
              </div>
            )}
          </CardContent>
        </Card>

        <div className="flex justify-end gap-4 mt-6">
          <Link href={`/devis/${devisId}`}>
            <Button variant="outline" type="button">
              Annuler
            </Button>
          </Link>
          <Button type="submit" disabled={isSaving}>
            <Save className="mr-2 h-4 w-4" />
            {isSaving ? "Enregistrement..." : "Enregistrer"}
          </Button>
        </div>
      </form>
    </div>
  )
}
