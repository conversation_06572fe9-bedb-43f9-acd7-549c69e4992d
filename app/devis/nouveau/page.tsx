"use client"

import type React from "react"

import { useState } from "react"
import { useRout<PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Calendar } from "@/components/ui/calendar"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { fr } from "date-fns/locale"
import { format } from "date-fns"
import { CalendarIcon, ArrowLeft, Save, Plus, Trash2 } from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { createDevis, createS<PERSON><PERSON><PERSON>ev<PERSON>, createLigneDev<PERSON> } from "@/services/devis"
import Link from "next/link"

interface Section {
  id: number
  titre: string
  description: string
  lignes: Ligne[]
}

interface Ligne {
  id: number
  designation: string
  description: string
  quantite: number
  unite: string
  prix_unitaire_ht: number
  taux_tva: number
}

export default function NouveauDevisPage() {
  const router = useRouter()
  const { toast } = useToast()
  const [isSaving, setIsSaving] = useState(false)
  const [sections, setSections] = useState<Section[]>([
    {
      id: 1,
      titre: "Préparation du chantier",
      description: "Travaux préparatoires",
      lignes: [
        {
          id: 1,
          designation: "Installation du chantier",
          description: "Mise en place des équipements et sécurisation",
          quantite: 1,
          unite: "forfait",
          prix_unitaire_ht: 350,
          taux_tva: 20,
        },
      ],
    },
  ])
  const [formData, setFormData] = useState({
    reference: `DEV-${new Date().getFullYear()}-${String(Date.now()).slice(-6)}`,
    date_validite: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 jours
    statut: "brouillon" as const,
    introduction: "Suite à votre demande, nous vous proposons le devis suivant :",
    conclusion:
      "En espérant que cette proposition réponde à vos attentes, nous restons à votre disposition pour tout complément d'information.",
    taux_remise: 0,
    validite_jours: 30,
    conditions_acceptees: false,
    projet_id: 1, // Default project ID
    utilisateur_id: 1, // Default user ID
  })

  const calculateTotals = () => {
    const totalHT = sections.reduce((sectionTotal, section) => {
      return (
        sectionTotal +
        section.lignes.reduce((ligneTotal, ligne) => {
          return ligneTotal + ligne.quantite * ligne.prix_unitaire_ht
        }, 0)
      )
    }, 0)

    const totalTVA = sections.reduce((sectionTotal, section) => {
      return (
        sectionTotal +
        section.lignes.reduce((ligneTotal, ligne) => {
          const montantHT = ligne.quantite * ligne.prix_unitaire_ht
          return ligneTotal + (montantHT * ligne.taux_tva) / 100
        }, 0)
      )
    }, 0)

    return {
      totalHT,
      totalTVA,
      totalTTC: totalHT + totalTVA,
    }
  }

  const { totalHT, totalTTC } = calculateTotals()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSaving(true)

    try {
      // Create the devis first
      const newDevis = await createDevis({
        reference: formData.reference,
        date_validite: formData.date_validite.toISOString(),
        statut: formData.statut,
        introduction: formData.introduction,
        conclusion: formData.conclusion,
        montant_ht: totalHT,
        montant_ttc: totalTTC,
        taux_remise: formData.taux_remise,
        validite_jours: formData.validite_jours,
        conditions_acceptees: formData.conditions_acceptees,
        projet_id: formData.projet_id,
        utilisateur_id: formData.utilisateur_id,
        date_signature: null,
        signature_url: null,
      })

      // Create sections and lines
      for (const [sectionIndex, section] of sections.entries()) {
        const newSection = await createSectionDevis({
          devis_id: newDevis.id,
          titre: section.titre,
          description: section.description,
          ordre: sectionIndex + 1,
          montant_ht: section.lignes.reduce((total, ligne) => total + ligne.quantite * ligne.prix_unitaire_ht, 0),
        })

        for (const [ligneIndex, ligne] of section.lignes.entries()) {
          await createLigneDevis({
            section_id: newSection.id,
            catalogue_item_id: null,
            designation: ligne.designation,
            reference: "",
            description: ligne.description,
            quantite: ligne.quantite,
            unite: ligne.unite,
            prix_unitaire_ht: ligne.prix_unitaire_ht,
            taux_tva: ligne.taux_tva,
            montant_ht: ligne.quantite * ligne.prix_unitaire_ht,
            montant_ttc: ligne.quantite * ligne.prix_unitaire_ht * (1 + ligne.taux_tva / 100),
            ordre: ligneIndex + 1,
          })
        }
      }

      toast({
        title: "Succès",
        description: "Devis créé avec succès",
      })

      router.push(`/devis/${newDevis.id}`)
    } catch (error: any) {
      toast({
        title: "Erreur",
        description: error.message || "Impossible de créer le devis",
        variant: "destructive",
      })
    } finally {
      setIsSaving(false)
    }
  }

  const addSection = () => {
    setSections([
      ...sections,
      {
        id: Date.now(),
        titre: "Nouvelle section",
        description: "",
        lignes: [],
      },
    ])
  }

  const updateSection = (sectionId: number, updates: Partial<Section>) => {
    setSections(sections.map((section) => (section.id === sectionId ? { ...section, ...updates } : section)))
  }

  const deleteSection = (sectionId: number) => {
    setSections(sections.filter((section) => section.id !== sectionId))
  }

  const addLigne = (sectionId: number) => {
    setSections(
      sections.map((section) => {
        if (section.id === sectionId) {
          return {
            ...section,
            lignes: [
              ...section.lignes,
              {
                id: Date.now(),
                designation: "Nouvelle ligne",
                description: "",
                quantite: 1,
                unite: "unité",
                prix_unitaire_ht: 0,
                taux_tva: 20,
              },
            ],
          }
        }
        return section
      }),
    )
  }

  const updateLigne = (sectionId: number, ligneId: number, updates: Partial<Ligne>) => {
    setSections(
      sections.map((section) => {
        if (section.id === sectionId) {
          return {
            ...section,
            lignes: section.lignes.map((ligne) => (ligne.id === ligneId ? { ...ligne, ...updates } : ligne)),
          }
        }
        return section
      }),
    )
  }

  const deleteLigne = (sectionId: number, ligneId: number) => {
    setSections(
      sections.map((section) => {
        if (section.id === sectionId) {
          return {
            ...section,
            lignes: section.lignes.filter((ligne) => ligne.id !== ligneId),
          }
        }
        return section
      }),
    )
  }

  return (
    <div className="flex flex-col gap-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Link href="/devis">
            <Button variant="outline" size="sm">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Retour
            </Button>
          </Link>
          <div>
            <h1 className="text-2xl font-bold">Nouveau devis</h1>
            <p className="text-muted-foreground">Créez un nouveau devis</p>
          </div>
        </div>
      </div>

      <form onSubmit={handleSubmit}>
        <div className="grid gap-6 md:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle>Informations générales</CardTitle>
              <CardDescription>Informations de base du devis</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="reference">Référence</Label>
                <Input
                  id="reference"
                  value={formData.reference}
                  onChange={(e) => setFormData({ ...formData, reference: e.target.value })}
                  placeholder="DEV-2023-043"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="date_validite">Date de validité</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button variant="outline" className="w-full justify-start text-left font-normal">
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {formData.date_validite
                        ? format(formData.date_validite, "PPP", { locale: fr })
                        : "Sélectionner une date"}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={formData.date_validite}
                      onSelect={(date) => date && setFormData({ ...formData, date_validite: date })}
                      locale={fr}
                    />
                  </PopoverContent>
                </Popover>
              </div>

              <div className="space-y-2">
                <Label htmlFor="statut">Statut</Label>
                <Select
                  value={formData.statut}
                  onValueChange={(value) => setFormData({ ...formData, statut: value as any })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="brouillon">Brouillon</SelectItem>
                    <SelectItem value="envoyé">Envoyé</SelectItem>
                    <SelectItem value="signé">Signé</SelectItem>
                    <SelectItem value="refusé">Refusé</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="validite_jours">Validité (jours)</Label>
                <Input
                  id="validite_jours"
                  type="number"
                  value={formData.validite_jours}
                  onChange={(e) => setFormData({ ...formData, validite_jours: Number.parseInt(e.target.value) || 30 })}
                  min="1"
                  max="365"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="projet_id">Projet ID</Label>
                <Input
                  id="projet_id"
                  type="number"
                  value={formData.projet_id}
                  onChange={(e) => setFormData({ ...formData, projet_id: Number.parseInt(e.target.value) || 1 })}
                  min="1"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="utilisateur_id">Utilisateur ID</Label>
                <Input
                  id="utilisateur_id"
                  type="number"
                  value={formData.utilisateur_id}
                  onChange={(e) => setFormData({ ...formData, utilisateur_id: Number.parseInt(e.target.value) || 1 })}
                  min="1"
                />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Résumé financier</CardTitle>
              <CardDescription>Calculé automatiquement à partir des lignes</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label>Total HT (calculé)</Label>
                <div className="text-lg font-semibold">{totalHT.toFixed(2)} €</div>
              </div>

              <div className="space-y-2">
                <Label>Total TTC (calculé)</Label>
                <div className="text-lg font-semibold">{totalTTC.toFixed(2)} €</div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="taux_remise">Taux de remise (%)</Label>
                <Input
                  id="taux_remise"
                  type="number"
                  step="0.01"
                  value={formData.taux_remise}
                  onChange={(e) => setFormData({ ...formData, taux_remise: Number.parseFloat(e.target.value) || 0 })}
                  min="0"
                  max="100"
                />
              </div>
            </CardContent>
          </Card>

          <Card className="md:col-span-2">
            <CardHeader>
              <CardTitle>Contenu</CardTitle>
              <CardDescription>Introduction et conclusion du devis</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="introduction">Introduction</Label>
                <Textarea
                  id="introduction"
                  value={formData.introduction}
                  onChange={(e) => setFormData({ ...formData, introduction: e.target.value })}
                  placeholder="Suite à votre demande, nous vous proposons..."
                  rows={3}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="conclusion">Conclusion</Label>
                <Textarea
                  id="conclusion"
                  value={formData.conclusion}
                  onChange={(e) => setFormData({ ...formData, conclusion: e.target.value })}
                  placeholder="En espérant que cette proposition réponde à vos attentes..."
                  rows={3}
                />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Sections Management */}
        <Card className="mt-6">
          <CardHeader className="flex flex-row items-center justify-between">
            <div>
              <CardTitle>Sections du devis</CardTitle>
              <CardDescription>Ajoutez des sections et des lignes à votre devis</CardDescription>
            </div>
            <Button type="button" onClick={addSection}>
              <Plus className="mr-2 h-4 w-4" />
              Ajouter une section
            </Button>
          </CardHeader>
          <CardContent className="space-y-6">
            {sections.map((section, sectionIndex) => (
              <div key={section.id} className="rounded-md border p-4">
                <div className="flex items-center justify-between mb-4">
                  <div className="font-medium">Section {sectionIndex + 1}</div>
                  <Button type="button" variant="ghost" size="icon" onClick={() => deleteSection(section.id)}>
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>

                <div className="grid gap-4 mb-4">
                  <div className="space-y-2">
                    <Label>Titre</Label>
                    <Input
                      value={section.titre}
                      onChange={(e) => updateSection(section.id, { titre: e.target.value })}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label>Description</Label>
                    <Textarea
                      value={section.description}
                      onChange={(e) => updateSection(section.id, { description: e.target.value })}
                    />
                  </div>
                </div>

                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-[300px]">Désignation</TableHead>
                      <TableHead>Quantité</TableHead>
                      <TableHead>Unité</TableHead>
                      <TableHead>Prix unitaire HT</TableHead>
                      <TableHead>TVA</TableHead>
                      <TableHead>Total HT</TableHead>
                      <TableHead></TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {section.lignes.map((ligne) => (
                      <TableRow key={ligne.id}>
                        <TableCell>
                          <Input
                            value={ligne.designation}
                            onChange={(e) => updateLigne(section.id, ligne.id, { designation: e.target.value })}
                          />
                        </TableCell>
                        <TableCell>
                          <Input
                            type="number"
                            value={ligne.quantite}
                            onChange={(e) =>
                              updateLigne(section.id, ligne.id, { quantite: Number.parseFloat(e.target.value) || 0 })
                            }
                          />
                        </TableCell>
                        <TableCell>
                          <Select
                            value={ligne.unite}
                            onValueChange={(value) => updateLigne(section.id, ligne.id, { unite: value })}
                          >
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="unité">Unité</SelectItem>
                              <SelectItem value="m²">m²</SelectItem>
                              <SelectItem value="m">m</SelectItem>
                              <SelectItem value="forfait">Forfait</SelectItem>
                              <SelectItem value="heure">Heure</SelectItem>
                            </SelectContent>
                          </Select>
                        </TableCell>
                        <TableCell>
                          <Input
                            type="number"
                            step="0.01"
                            value={ligne.prix_unitaire_ht}
                            onChange={(e) =>
                              updateLigne(section.id, ligne.id, {
                                prix_unitaire_ht: Number.parseFloat(e.target.value) || 0,
                              })
                            }
                          />
                        </TableCell>
                        <TableCell>
                          <Select
                            value={ligne.taux_tva.toString()}
                            onValueChange={(value) =>
                              updateLigne(section.id, ligne.id, { taux_tva: Number.parseFloat(value) })
                            }
                          >
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="20">20%</SelectItem>
                              <SelectItem value="10">10%</SelectItem>
                              <SelectItem value="5.5">5.5%</SelectItem>
                              <SelectItem value="0">0%</SelectItem>
                            </SelectContent>
                          </Select>
                        </TableCell>
                        <TableCell className="font-medium">
                          {(ligne.quantite * ligne.prix_unitaire_ht).toFixed(2)} €
                        </TableCell>
                        <TableCell>
                          <Button
                            type="button"
                            variant="ghost"
                            size="icon"
                            onClick={() => deleteLigne(section.id, ligne.id)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>

                <div className="mt-4">
                  <Button type="button" variant="outline" onClick={() => addLigne(section.id)}>
                    <Plus className="mr-2 h-4 w-4" />
                    Ajouter une ligne
                  </Button>
                </div>
              </div>
            ))}
          </CardContent>
        </Card>

        <div className="flex justify-end gap-4 mt-6">
          <Link href="/devis">
            <Button variant="outline" type="button">
              Annuler
            </Button>
          </Link>
          <Button type="submit" disabled={isSaving}>
            <Save className="mr-2 h-4 w-4" />
            {isSaving ? "Création..." : "Créer le devis"}
          </Button>
        </div>
      </form>
    </div>
  )
}
