import { NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"

export async function GET() {
  try {
    // Initialize Supabase client directly in this function
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

    if (!supabaseUrl || !supabaseServiceKey) {
      return NextResponse.json(
        {
          success: false,
          error: "Variables d'environnement Supabase manquantes",
          details: {
            urlDefined: !!supabaseUrl,
            keyDefined: !!supabaseServiceKey,
          },
        },
        { status: 500 },
      )
    }

    // Create a new Supabase client with the service role key
    const supabase = createClient(supabaseUrl, supabaseServiceKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false,
      },
    })

    // Check if the bucket exists
    const bucketName = "client-files"
    const { data: buckets, error: bucketsError } = await supabase.storage.listBuckets()

    if (bucketsError) {
      return NextResponse.json(
        {
          success: false,
          error: "Erreur lors de la récupération des buckets",
          details: bucketsError,
        },
        { status: 500 },
      )
    }

    const bucketExists = buckets.some((bucket) => bucket.name === bucketName)

    if (!bucketExists) {
      return NextResponse.json(
        {
          success: false,
          exists: false,
          error: `Le bucket '${bucketName}' n'existe pas`,
        },
        { status: 404 },
      )
    }

    // Check bucket policies
    const { data: policies, error: policiesError } = await supabase.rpc("get_policies_for_bucket", {
      bucket_name: bucketName,
    })

    // If the RPC function doesn't exist, we'll just return that the bucket exists
    if (policiesError) {
      return NextResponse.json({
        success: true,
        exists: true,
        canCheckPolicies: false,
        message: `Le bucket '${bucketName}' existe, mais impossible de vérifier les politiques`,
      })
    }

    return NextResponse.json({
      success: true,
      exists: true,
      canCheckPolicies: true,
      policies,
      message: `Le bucket '${bucketName}' existe`,
    })
  } catch (error) {
    console.error("Error checking bucket:", error)
    return NextResponse.json(
      {
        success: false,
        error: "Erreur lors de la vérification du bucket",
        details: error.message || "Erreur inconnue",
      },
      { status: 500 },
    )
  }
}
