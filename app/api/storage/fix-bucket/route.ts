import { NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"

export async function POST(request: Request) {
  try {
    // Initialize Supabase client with service role key
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY
    const bucketName = process.env.NEXT_PUBLIC_SUPABASE_CLIENT_FILES_BUCKET || "client-files"

    if (!supabaseUrl || !supabaseServiceKey) {
      return NextResponse.json(
        {
          success: false,
          error: "Variables d'environnement Supabase manquantes",
        },
        { status: 500 },
      )
    }

    // Create a new Supabase client with the service role key
    const supabase = createClient(supabaseUrl, supabaseServiceKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false,
      },
    })

    // Check if the bucket exists
    const { data: buckets, error: bucketsError } = await supabase.storage.listBuckets()

    if (bucketsError) {
      return NextResponse.json(
        {
          success: false,
          error: "Erreur lors de la récupération des buckets",
          details: bucketsError,
        },
        { status: 500 },
      )
    }

    const bucketExists = buckets.some((bucket) => bucket.name === bucketName)

    // Create the bucket if it doesn't exist
    if (!bucketExists) {
      const { data: newBucket, error: createError } = await supabase.storage.createBucket(bucketName, {
        public: true,
      })

      if (createError) {
        return NextResponse.json(
          {
            success: false,
            error: "Erreur lors de la création du bucket",
            details: createError,
          },
          { status: 500 },
        )
      }

      // Execute SQL to create policies
      const createPoliciesSQL = `
        -- Allow authenticated users to upload files
        CREATE POLICY "Allow authenticated users to upload files"
        ON storage.objects FOR INSERT TO authenticated USING (
          bucket_id = '${bucketName}' AND auth.role() = 'authenticated'
        );

        -- Allow users to select files
        CREATE POLICY "Allow users to select files"
        ON storage.objects FOR SELECT TO authenticated USING (
          bucket_id = '${bucketName}'
        );

        -- Allow users to update files
        CREATE POLICY "Allow users to update files"
        ON storage.objects FOR UPDATE TO authenticated USING (
          bucket_id = '${bucketName}'
        );

        -- Allow users to delete files
        CREATE POLICY "Allow users to delete files"
        ON storage.objects FOR DELETE TO authenticated USING (
          bucket_id = '${bucketName}'
        );
      `

      // Execute the SQL to create policies
      const { error: policiesError } = await supabase.rpc("exec_sql", { sql: createPoliciesSQL })

      if (policiesError) {
        return NextResponse.json({
          success: true,
          bucketCreated: true,
          policiesCreated: false,
          message: "Bucket créé mais erreur lors de la création des politiques",
          error: policiesError,
        })
      }

      return NextResponse.json({
        success: true,
        bucketCreated: true,
        policiesCreated: true,
        message: "Bucket et politiques créés avec succès",
      })
    }

    // If the bucket already exists, check and fix policies
    // This is a simplified approach - in a real app, you'd want to check if each policy exists
    const createPoliciesSQL = `
      -- Allow authenticated users to upload files
      CREATE POLICY IF NOT EXISTS "Allow authenticated users to upload files"
      ON storage.objects FOR INSERT TO authenticated USING (
        bucket_id = '${bucketName}' AND auth.role() = 'authenticated'
      );

      -- Allow users to select files
      CREATE POLICY IF NOT EXISTS "Allow users to select files"
      ON storage.objects FOR SELECT TO authenticated USING (
        bucket_id = '${bucketName}'
      );

      -- Allow users to update files
      CREATE POLICY IF NOT EXISTS "Allow users to update files"
      ON storage.objects FOR UPDATE TO authenticated USING (
        bucket_id = '${bucketName}'
      );

      -- Allow users to delete files
      CREATE POLICY IF NOT EXISTS "Allow users to delete files"
      ON storage.objects FOR DELETE TO authenticated USING (
        bucket_id = '${bucketName}'
      );
    `

    // Execute the SQL to create or update policies
    const { error: policiesError } = await supabase.rpc("exec_sql", { sql: createPoliciesSQL })

    if (policiesError) {
      return NextResponse.json({
        success: true,
        bucketExists: true,
        policiesUpdated: false,
        message: "Le bucket existe mais erreur lors de la mise à jour des politiques",
        error: policiesError,
      })
    }

    return NextResponse.json({
      success: true,
      bucketExists: true,
      policiesUpdated: true,
      message: "Le bucket existe et les politiques ont été mises à jour",
    })
  } catch (error) {
    console.error("Error fixing bucket:", error)
    return NextResponse.json(
      {
        success: false,
        error: "Erreur lors de la réparation du bucket",
        details: error.message || "Erreur inconnue",
      },
      { status: 500 },
    )
  }
}
