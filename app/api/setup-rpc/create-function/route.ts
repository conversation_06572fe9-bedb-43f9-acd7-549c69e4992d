import { createClient } from "@supabase/supabase-js"
import { NextResponse } from "next/server"

// Utiliser directement les variables d'environnement
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

export async function GET() {
  try {
    // Créer un client Supabase avec la clé de service si disponible
    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // SQL pour créer la fonction exec_sql
    const createFunctionSQL = `
      CREATE OR REPLACE FUNCTION exec_sql(sql_query text)
      RETURNS void
      LANGUAGE plpgsql
      SECURITY DEFINER
      AS $$
      BEGIN
        EXECUTE sql_query;
      END;
      $$;
    `

    // Exécuter le SQL directement
    const { error } = await supabase.rpc("exec_sql", { sql_query: createFunctionSQL })

    if (error) {
      // Si la fonction exec_sql n'existe pas encore, on doit la créer autrement
      // Ceci est une simplification et pourrait ne pas fonctionner dans tous les cas
      const { error: directError } = await supabase.from("_exec_sql_temp").select("*").limit(1)

      if (directError) {
        return NextResponse.json(
          {
            success: false,
            message:
              "Impossible de créer la fonction exec_sql automatiquement. Veuillez la créer manuellement dans l'interface SQL de Supabase.",
            error: directError.message,
          },
          { status: 500 },
        )
      }
    }

    return NextResponse.json({
      success: true,
      message:
        "Fonction exec_sql créée ou déjà existante. Vous pouvez maintenant créer les tables de la base de données.",
    })
  } catch (error: any) {
    return NextResponse.json(
      {
        success: false,
        message: "Une erreur s'est produite lors de la création de la fonction exec_sql.",
        error: error.message,
      },
      { status: 500 },
    )
  }
}
