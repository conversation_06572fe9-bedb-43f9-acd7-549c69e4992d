import { type NextRequest, NextResponse } from "next/server"
import { convertAnalysisToDevis } from "@/services/agent"
import { createClient } from "@supabase/supabase-js"

export async function POST(request: NextRequest) {
  try {
    const { requestId } = await request.json()

    if (!requestId || isNaN(requestId)) {
      return NextResponse.json({ error: "ID de demande invalide" }, { status: 400 })
    }

    // Create service role client to get user info
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

    if (!supabaseUrl || !supabaseServiceKey) {
      return NextResponse.json({ error: "Configuration Supabase manquante" }, { status: 500 })
    }

    const serviceSupabase = createClient(supabaseUrl, supabaseServiceKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false,
      },
    })

    // Get the first available user or create a default one
    const { data: users, error: usersError } = await serviceSupabase.from("utilisateurs").select("id").limit(1)

    if (usersError) {
      console.error("Erreur lors de la récupération des utilisateurs:", usersError)
      return NextResponse.json({ error: "Erreur lors de la récupération des utilisateurs" }, { status: 500 })
    }

    let userId: number

    if (users && users.length > 0) {
      userId = users[0].id
    } else {
      // Create a default user if none exists
      const { data: newUser, error: createUserError } = await serviceSupabase
        .from("utilisateurs")
        .insert([
          {
            nom: "Système",
            prenom: "IA",
            email: "<EMAIL>",
            role: "admin",
            entreprise_id: 1, // Assuming default enterprise exists
          },
        ])
        .select()
        .single()

      if (createUserError) {
        console.error("Erreur lors de la création de l'utilisateur par défaut:", createUserError)
        return NextResponse.json({ error: "Erreur lors de la création de l'utilisateur" }, { status: 500 })
      }

      userId = newUser.id
    }

    const result = await convertAnalysisToDevis(requestId, userId)

    return NextResponse.json(result)
  } catch (error: any) {
    console.error("Erreur lors de la conversion en devis:", error)
    return NextResponse.json({ error: error.message || "Erreur lors de la conversion en devis" }, { status: 500 })
  }
}
