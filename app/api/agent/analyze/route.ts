import { type NextRequest, NextResponse } from "next/server"
import { analyzeRequestWithAI } from "@/services/agent"

export async function POST(request: NextRequest) {
  try {
    const { requestId } = await request.json()

    if (!requestId || isNaN(requestId)) {
      return NextResponse.json({ error: "ID de demande invalide" }, { status: 400 })
    }

    const analysis = await analyzeRequestWithAI(requestId)

    return NextResponse.json(analysis)
  } catch (error: any) {
    console.error("Erreur lors de l'analyse IA:", error)
    return NextResponse.json({ error: error.message || "Erreur lors de l'analyse IA" }, { status: 500 })
  }
}
