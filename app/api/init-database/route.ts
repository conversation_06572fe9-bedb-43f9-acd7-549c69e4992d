import { createClient } from "@supabase/supabase-js"
import { NextResponse } from "next/server"

// Utiliser directement les variables d'environnement
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

export async function GET() {
  try {
    // Créer un client Supabase avec la clé de service si disponible
    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Vérifier si les tables existent déjà
    const { data: tables, error: tablesError } = await supabase
      .from("information_schema.tables")
      .select("table_name")
      .eq("table_schema", "public")

    if (tablesError) {
      return NextResponse.json(
        {
          success: false,
          message: "Erreur lors de la vérification des tables existantes",
          error: tablesError.message,
        },
        { status: 500 },
      )
    }

    const existingTables = tables.map((t) => t.table_name)
    const requiredTables = [
      "entreprises",
      "utilisateurs",
      "clients",
      "projets",
      "devis",
      "sections_devis",
      "lignes_devis",
      "catalogue",
      "factures",
      "documents",
      "notes",
      "cctp_documents",
      "cctp_sections",
      "notifications",
    ]

    // Vérifier si toutes les tables requises existent
    const missingTables = requiredTables.filter((table) => !existingTables.includes(table))

    if (missingTables.length === 0) {
      return NextResponse.json({
        success: true,
        message: "Toutes les tables requises existent déjà",
        existingTables,
      })
    }

    // Créer la fonction exec_sql si elle n'existe pas
    const createFunctionSQL = `
      CREATE OR REPLACE FUNCTION exec_sql(sql_query text)
      RETURNS void
      LANGUAGE plpgsql
      SECURITY DEFINER
      AS $$
      BEGIN
        EXECUTE sql_query;
      END;
      $$;
    `

    const { error: functionError } = await supabase.rpc("exec_sql", { sql_query: createFunctionSQL }).catch(() => {
      // Si la fonction n'existe pas encore, exécuter directement le SQL
      return supabase.from("_exec_sql_temp").select("*").limit(1)
    })

    // Lire le contenu du fichier SQL
    const sqlContent = `
    -- Table ENTREPRISE
    CREATE TABLE IF NOT EXISTS entreprises (
      id SERIAL PRIMARY KEY,
      nom VARCHAR(255) NOT NULL,
      siret VARCHAR(14),
      adresse TEXT,
      code_postal VARCHAR(10),
      ville VARCHAR(100),
      telephone VARCHAR(20),
      email VARCHAR(255),
      logo_url TEXT,
      site_web VARCHAR(255),
      cgv TEXT,
      date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      devise VARCHAR(3) DEFAULT 'EUR',
      actif BOOLEAN DEFAULT TRUE
    );

    -- Table UTILISATEUR
    CREATE TABLE IF NOT EXISTS utilisateurs (
      id SERIAL PRIMARY KEY,
      entreprise_id INTEGER REFERENCES entreprises(id),
      nom VARCHAR(100),
      prenom VARCHAR(100),
      email VARCHAR(255) NOT NULL UNIQUE,
      telephone VARCHAR(20),
      role VARCHAR(50) DEFAULT 'collaborateur',
      mot_de_passe_hash VARCHAR(255),
      derniere_connexion TIMESTAMP,
      actif BOOLEAN DEFAULT TRUE,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );

    -- Table CLIENT
    CREATE TABLE IF NOT EXISTS clients (
      id SERIAL PRIMARY KEY,
      entreprise_id INTEGER REFERENCES entreprises(id),
      type VARCHAR(20) CHECK (type IN ('particulier', 'professionnel')),
      nom VARCHAR(100),
      prenom VARCHAR(100),
      societe VARCHAR(255),
      siret VARCHAR(14),
      adresse_facturation TEXT,
      adresse_chantier TEXT,
      telephone VARCHAR(20),
      email VARCHAR(255),
      notes TEXT,
      date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      actif BOOLEAN DEFAULT TRUE
    );

    -- Table PROJET
    CREATE TABLE IF NOT EXISTS projets (
      id SERIAL PRIMARY KEY,
      client_id INTEGER REFERENCES clients(id),
      utilisateur_id INTEGER REFERENCES utilisateurs(id),
      reference VARCHAR(50) UNIQUE,
      nom VARCHAR(255) NOT NULL,
      adresse_chantier TEXT,
      date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      date_debut_prevu DATE,
      date_fin_prevu DATE,
      date_debut_reel DATE,
      date_fin_reel DATE,
      statut VARCHAR(20) CHECK (statut IN ('devis', 'signé', 'en_cours', 'terminé', 'facturé')),
      description TEXT,
      montant_total_ht DECIMAL(10, 2),
      montant_total_ttc DECIMAL(10, 2)
    );

    -- Table DEVIS
    CREATE TABLE IF NOT EXISTS devis (
      id SERIAL PRIMARY KEY,
      projet_id INTEGER REFERENCES projets(id),
      utilisateur_id INTEGER REFERENCES utilisateurs(id),
      reference VARCHAR(50) UNIQUE,
      date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      date_validite DATE,
      date_signature TIMESTAMP,
      statut VARCHAR(20) CHECK (statut IN ('brouillon', 'envoyé', 'signé', 'refusé')),
      introduction TEXT,
      conclusion TEXT,
      taux_remise DECIMAL(5, 2) DEFAULT 0,
      montant_ht DECIMAL(10, 2),
      montant_ttc DECIMAL(10, 2),
      validite_jours INTEGER DEFAULT 30,
      conditions_acceptees BOOLEAN DEFAULT FALSE,
      signature_url TEXT
    );

    -- Table SECTION_DEVIS
    CREATE TABLE IF NOT EXISTS sections_devis (
      id SERIAL PRIMARY KEY,
      devis_id INTEGER REFERENCES devis(id),
      titre VARCHAR(255) NOT NULL,
      description TEXT,
      ordre INTEGER,
      montant_ht DECIMAL(10, 2)
    );

    -- Table CATALOGUE
    CREATE TABLE IF NOT EXISTS catalogue (
      id SERIAL PRIMARY KEY,
      entreprise_id INTEGER REFERENCES entreprises(id),
      nom VARCHAR(255) NOT NULL,
      type VARCHAR(50) CHECK (type IN ('matériaux', 'main d''œuvre', 'prestation')),
      reference VARCHAR(50),
      description TEXT,
      prix_unitaire_ht DECIMAL(10, 2) NOT NULL,
      unite VARCHAR(20),
      taux_tva DECIMAL(5, 2) DEFAULT 20,
      actif BOOLEAN DEFAULT TRUE,
      categorie VARCHAR(100),
      sous_categorie VARCHAR(100)
    );

    -- Table LIGNE_DEVIS
    CREATE TABLE IF NOT EXISTS lignes_devis (
      id SERIAL PRIMARY KEY,
      section_id INTEGER REFERENCES sections_devis(id),
      catalogue_item_id INTEGER REFERENCES catalogue(id),
      designation VARCHAR(255) NOT NULL,
      reference VARCHAR(50),
      description TEXT,
      quantite DECIMAL(10, 3) NOT NULL,
      unite VARCHAR(20),
      prix_unitaire_ht DECIMAL(10, 2) NOT NULL,
      taux_tva DECIMAL(5, 2) DEFAULT 20,
      montant_ht DECIMAL(10, 2),
      montant_ttc DECIMAL(10, 2),
      ordre INTEGER
    );

    -- Table FACTURE
    CREATE TABLE IF NOT EXISTS factures (
      id SERIAL PRIMARY KEY,
      devis_id INTEGER REFERENCES devis(id),
      reference VARCHAR(50) UNIQUE,
      type VARCHAR(20) CHECK (type IN ('acompte', 'situation', 'solde')),
      date_emission TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      date_echeance DATE,
      montant_ht DECIMAL(10, 2),
      montant_ttc DECIMAL(10, 2),
      statut VARCHAR(20) CHECK (statut IN ('brouillon', 'envoyée', 'payée', 'impayée')),
      date_paiement TIMESTAMP,
      mode_paiement VARCHAR(50)
    );

    -- Table DOCUMENT
    CREATE TABLE IF NOT EXISTS documents (
      id SERIAL PRIMARY KEY,
      projet_id INTEGER REFERENCES projets(id),
      utilisateur_id INTEGER REFERENCES utilisateurs(id),
      type VARCHAR(20) CHECK (type IN ('photo', 'note', 'audio', 'pdf', 'plan')),
      nom_fichier VARCHAR(255),
      url_stockage TEXT,
      mimetype VARCHAR(100),
      date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      description TEXT,
      tags VARCHAR(255),
      transcript TEXT,
      taille_ko DECIMAL(10, 2)
    );

    -- Table NOTE
    CREATE TABLE IF NOT EXISTS notes (
      id SERIAL PRIMARY KEY,
      projet_id INTEGER REFERENCES projets(id),
      utilisateur_id INTEGER REFERENCES utilisateurs(id),
      contenu TEXT,
      date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      date_modification TIMESTAMP,
      type VARCHAR(20) CHECK (type IN ('manuscrite', 'texte', 'transcription')),
      convertie_devis BOOLEAN DEFAULT FALSE
    );

    -- Table CCTP_DOCUMENT
    CREATE TABLE IF NOT EXISTS cctp_documents (
      id SERIAL PRIMARY KEY,
      projet_id INTEGER REFERENCES projets(id),
      reference VARCHAR(50),
      titre VARCHAR(255),
      contenu TEXT,
      date_import TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );

    -- Table CCTP_SECTION
    CREATE TABLE IF NOT EXISTS cctp_sections (
      id SERIAL PRIMARY KEY,
      cctp_document_id INTEGER REFERENCES cctp_documents(id),
      numero VARCHAR(20),
      titre VARCHAR(255),
      contenu TEXT,
      parent_id INTEGER REFERENCES cctp_sections(id)
    );

    -- Table NOTIFICATION
    CREATE TABLE IF NOT EXISTS notifications (
      id SERIAL PRIMARY KEY,
      utilisateur_id INTEGER REFERENCES utilisateurs(id),
      type VARCHAR(50),
      titre VARCHAR(255),
      message TEXT,
      date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      lu BOOLEAN DEFAULT FALSE,
      lien VARCHAR(255)
    );
    `

    // Exécuter le script SQL
    const { error: sqlError } = await supabase.rpc("exec_sql", { sql_query: sqlContent })

    if (sqlError) {
      return NextResponse.json(
        {
          success: false,
          message: "Erreur lors de la création des tables",
          error: sqlError.message,
        },
        { status: 500 },
      )
    }

    // Insérer des données de test si nécessaire
    const { data: entreprises } = await supabase.from("entreprises").select("*")

    if (!entreprises || entreprises.length === 0) {
      // Insérer une entreprise par défaut
      const { error: insertError } = await supabase.from("entreprises").insert([
        {
          nom: "BTP Expert",
          siret: "12345678901234",
          adresse: "123 rue des Artisans",
          code_postal: "75001",
          ville: "Paris",
          telephone: "01 23 45 67 89",
          email: "<EMAIL>",
          site_web: "www.btpexpert.fr",
          devise: "EUR",
          actif: true,
        },
      ])

      if (insertError) {
        console.error("Erreur lors de l'insertion des données de test:", insertError)
      }
    }

    return NextResponse.json({
      success: true,
      message: "Base de données initialisée avec succès",
      createdTables: missingTables,
    })
  } catch (error: any) {
    return NextResponse.json(
      {
        success: false,
        message: "Une erreur s'est produite lors de l'initialisation de la base de données",
        error: error.message,
      },
      { status: 500 },
    )
  }
}
