"use client"

import { <PERSON>, Card<PERSON>ontent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>hart,
  <PERSON><PERSON>hart,
  XAxis,
  YAxis,
  CartesianGrid,
  Legend,
  Bar,
  Line,
  Pie,
  Cell,
  ResponsiveContainer,
} from "recharts"
import { ChartContainer, ChartTooltip } from "@/components/ui/chart"
import { But<PERSON> } from "@/components/ui/button"
import { CalendarIcon, Download, FileText, Plus, Users } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Calendar } from "@/components/ui/calendar"
import { fr } from "date-fns/locale"
import { format } from "date-fns"
import { useState } from "react"
import { CustomChartTooltip } from "@/components/custom-chart-tooltip"

const devisData = [
  { mois: "Jan", montant: 12000, nombre: 5 },
  { mois: "Fév", montant: 18000, nombre: 7 },
  { mois: "Mar", montant: 15000, nombre: 6 },
  { mois: "Avr", montant: 22000, nombre: 9 },
  { mois: "Mai", montant: 20000, nombre: 8 },
  { mois: "Juin", montant: 25000, nombre: 10 },
]

const facturesData = [
  { mois: "Jan", payé: 10000, enAttente: 2000 },
  { mois: "Fév", payé: 15000, enAttente: 3000 },
  { mois: "Mar", payé: 12000, enAttente: 3000 },
  { mois: "Avr", payé: 18000, enAttente: 4000 },
  { mois: "Mai", payé: 16000, enAttente: 4000 },
  { mois: "Juin", payé: 20000, enAttente: 5000 },
]

const clientsData = [
  { name: "Particuliers", value: 65 },
  { name: "Professionnels", value: 35 },
]

const COLORS = ["#0088FE", "#00C49F", "#FFBB28", "#FF8042"]

const devisRecents = [
  { id: "DEV-2023-042", client: "Martin Dupont", montant: 3500, date: "2023-06-15", statut: "envoyé" },
  { id: "DEV-2023-041", client: "Entreprise ABC", montant: 12800, date: "2023-06-12", statut: "signé" },
  { id: "DEV-2023-040", client: "Sophie Lefebvre", montant: 2200, date: "2023-06-10", statut: "brouillon" },
]

export default function DashboardPage() {
  const [date, setDate] = useState<Date | undefined>(new Date())

  return (
    <div className="flex flex-col gap-4">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Tableau de bord</h1>
        <div className="flex items-center gap-2">
          <Popover>
            <PopoverTrigger asChild>
              <Button variant="outline" className="flex items-center gap-2">
                <CalendarIcon className="h-4 w-4" />
                {date ? format(date, "PPP", { locale: fr }) : "Sélectionner une date"}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0">
              <Calendar mode="single" selected={date} onSelect={setDate} locale={fr} />
            </PopoverContent>
          </Popover>
          <Button>
            <Download className="mr-2 h-4 w-4" />
            Exporter
          </Button>
        </div>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Chiffre d'affaires</CardTitle>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              className="h-4 w-4 text-muted-foreground"
            >
              <path d="M12 2v20M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6" />
            </svg>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">112 600 €</div>
            <p className="text-xs text-muted-foreground">+20.1% par rapport au mois dernier</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Devis en cours</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">12</div>
            <p className="text-xs text-muted-foreground">45 200 € de CA potentiel</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Clients</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">42</div>
            <p className="text-xs text-muted-foreground">+2 nouveaux ce mois-ci</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Taux de conversion</CardTitle>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              className="h-4 w-4 text-muted-foreground"
            >
              <path d="M22 12h-4l-3 9L9 3l-3 9H2" />
            </svg>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">68%</div>
            <p className="text-xs text-muted-foreground">+4% par rapport au mois dernier</p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="devis" className="space-y-4">
        <TabsList>
          <TabsTrigger value="devis">Devis</TabsTrigger>
          <TabsTrigger value="factures">Factures</TabsTrigger>
          <TabsTrigger value="clients">Clients</TabsTrigger>
        </TabsList>
        <TabsContent value="devis" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Devis (6 derniers mois)</CardTitle>
              <CardDescription>Montant total des devis émis et nombre de devis par mois</CardDescription>
            </CardHeader>
            <CardContent className="pl-2">
              <ChartContainer
                config={{
                  montant: {
                    label: "Montant (€)",
                    color: "hsl(var(--chart-1))",
                  },
                  nombre: {
                    label: "Nombre de devis",
                    color: "hsl(var(--chart-2))",
                  },
                }}
                className="aspect-[4/3] h-80"
              >
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={devisData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="mois" />
                    <YAxis yAxisId="left" orientation="left" stroke="var(--color-montant)" />
                    <YAxis yAxisId="right" orientation="right" stroke="var(--color-nombre)" />
                    <ChartTooltip content={<CustomChartTooltip />} />
                    <Legend />
                    <Bar yAxisId="left" dataKey="montant" fill="var(--color-montant)" name="Montant (€)" />
                    <Bar yAxisId="right" dataKey="nombre" fill="var(--color-nombre)" name="Nombre de devis" />
                  </BarChart>
                </ResponsiveContainer>
              </ChartContainer>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle>Devis récents</CardTitle>
              <Button size="sm">
                <Plus className="mr-2 h-4 w-4" />
                Nouveau devis
              </Button>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {devisRecents.map((devis) => (
                  <div key={devis.id} className="flex items-center justify-between rounded-lg border p-4">
                    <div className="space-y-1">
                      <p className="text-sm font-medium">{devis.client}</p>
                      <p className="text-sm text-muted-foreground">{devis.id}</p>
                    </div>
                    <div className="flex items-center gap-4">
                      <div className="text-right">
                        <p className="text-sm font-medium">{devis.montant.toLocaleString("fr-FR")} €</p>
                        <p className="text-sm text-muted-foreground">
                          {new Date(devis.date).toLocaleDateString("fr-FR")}
                        </p>
                      </div>
                      <Badge
                        variant={
                          devis.statut === "signé" ? "success" : devis.statut === "envoyé" ? "default" : "outline"
                        }
                      >
                        {devis.statut}
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="factures" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Factures (6 derniers mois)</CardTitle>
              <CardDescription>Montant des factures payées et en attente par mois</CardDescription>
            </CardHeader>
            <CardContent className="pl-2">
              <ChartContainer
                config={{
                  payé: {
                    label: "Payé (€)",
                    color: "hsl(var(--chart-1))",
                  },
                  enAttente: {
                    label: "En attente (€)",
                    color: "hsl(var(--chart-2))",
                  },
                }}
                className="aspect-[4/3] h-80"
              >
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={facturesData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="mois" />
                    <YAxis />
                    <ChartTooltip content={<CustomChartTooltip />} />
                    <Legend />
                    <Line type="monotone" dataKey="payé" stroke="var(--color-payé)" name="Payé (€)" />
                    <Line type="monotone" dataKey="enAttente" stroke="var(--color-enAttente)" name="En attente (€)" />
                  </LineChart>
                </ResponsiveContainer>
              </ChartContainer>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="clients" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Répartition des clients</CardTitle>
              <CardDescription>Répartition entre particuliers et professionnels</CardDescription>
            </CardHeader>
            <CardContent className="pl-2">
              <ChartContainer
                config={{
                  value: {
                    label: "Pourcentage",
                    color: "hsl(var(--chart-1))",
                  },
                }}
                className="aspect-[4/3] h-80"
              >
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={clientsData}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {clientsData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <ChartTooltip content={<CustomChartTooltip />} />
                  </PieChart>
                </ResponsiveContainer>
              </ChartContainer>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
