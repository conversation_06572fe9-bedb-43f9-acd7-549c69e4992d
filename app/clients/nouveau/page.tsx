"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { useRout<PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Tabs, TabsContent } from "@/components/ui/tabs"
import { Textarea } from "@/components/ui/textarea"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { useToast } from "@/hooks/use-toast"
import { createClient } from "@/services/clients"
import { getUserEnterprise } from "@/services/entreprises"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { AlertCircle } from "lucide-react"

export default function NouveauClientPage() {
  const router = useRouter()
  const { toast } = useToast()
  const [isLoading, setIsLoading] = useState(false)
  const [isInitializing, setIsInitializing] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [clientType, setClientType] = useState<"particulier" | "professionnel">("particulier")
  const [entrepriseId, setEntrepriseId] = useState<number | null>(null)

  // Fetch the user's enterprise ID
  useEffect(() => {
    const fetchEntrepriseId = async () => {
      try {
        setIsInitializing(true)
        setError(null)
        const entreprise = await getUserEnterprise()
        if (entreprise) {
          setEntrepriseId(entreprise.id)
        }
      } catch (error: any) {
        console.error("Erreur lors de la récupération de l'entreprise:", error)
        setError("Impossible de récupérer l'entreprise. Veuillez réessayer plus tard.")
      } finally {
        setIsInitializing(false)
      }
    }

    fetchEntrepriseId()
  }, [])

  // État pour les champs du formulaire
  const [formData, setFormData] = useState({
    // Champs communs
    type: "particulier" as "particulier" | "professionnel",
    telephone: "",
    email: "",
    adresse_facturation: "",
    adresse_chantier: "",
    notes: "",
    // Champs particulier
    nom: "",
    prenom: "",
    // Champs professionnel
    societe: "",
    siret: "",
  })

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleTypeChange = (value: "particulier" | "professionnel") => {
    setClientType(value)
    setFormData((prev) => ({ ...prev, type: value }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setError(null)

    try {
      if (!entrepriseId) {
        throw new Error("Aucune entreprise associée. Impossible de créer un client.")
      }

      // Créer un objet client à partir des données du formulaire
      const clientData = {
        entreprise_id: entrepriseId,
        type: formData.type,
        nom: formData.type === "particulier" ? formData.nom : "",
        prenom: formData.type === "particulier" ? formData.prenom : "",
        societe: formData.type === "professionnel" ? formData.societe : "",
        siret: formData.type === "professionnel" ? formData.siret : "",
        telephone: formData.telephone,
        email: formData.email,
        adresse_facturation: formData.adresse_facturation,
        adresse_chantier: formData.adresse_chantier,
        notes: formData.notes,
        actif: true,
      }

      await createClient(clientData)

      toast({
        title: "Client créé avec succès",
        description: "Le client a été ajouté à votre base de données",
      })

      router.push("/clients")
      router.refresh()
    } catch (error: any) {
      console.error("Erreur lors de la création du client:", error)
      setError(error.message || "Une erreur est survenue lors de la création du client")
      toast({
        title: "Erreur",
        description: error.message || "Une erreur est survenue lors de la création du client",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="flex flex-col gap-4">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Nouveau client</h1>
      </div>

      {error && (
        <Alert variant="destructive" className="mb-4">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <form onSubmit={handleSubmit}>
        <Card>
          <CardHeader>
            <CardTitle>Informations du client</CardTitle>
            <CardDescription>Ajoutez un nouveau client à votre base de données</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-2">
              <Label>Type de client</Label>
              <RadioGroup
                value={clientType}
                onValueChange={(value) => handleTypeChange(value as "particulier" | "professionnel")}
                className="flex space-x-4"
              >
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="particulier" id="particulier" />
                  <Label htmlFor="particulier" className="cursor-pointer">
                    Particulier
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="professionnel" id="professionnel" />
                  <Label htmlFor="professionnel" className="cursor-pointer">
                    Professionnel
                  </Label>
                </div>
              </RadioGroup>
            </div>

            <Tabs value={clientType} className="w-full">
              <TabsContent value="particulier" className="space-y-4 mt-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="nom">Nom</Label>
                    <Input
                      id="nom"
                      name="nom"
                      value={formData.nom}
                      onChange={handleChange}
                      placeholder="Dupont"
                      required={clientType === "particulier"}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="prenom">Prénom</Label>
                    <Input
                      id="prenom"
                      name="prenom"
                      value={formData.prenom}
                      onChange={handleChange}
                      placeholder="Jean"
                      required={clientType === "particulier"}
                    />
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="professionnel" className="space-y-4 mt-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="societe">Société</Label>
                    <Input
                      id="societe"
                      name="societe"
                      value={formData.societe}
                      onChange={handleChange}
                      placeholder="Entreprise ABC"
                      required={clientType === "professionnel"}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="siret">SIRET</Label>
                    <Input
                      id="siret"
                      name="siret"
                      value={formData.siret}
                      onChange={handleChange}
                      placeholder="12345678901234"
                      maxLength={14}
                    />
                  </div>
                </div>
              </TabsContent>
            </Tabs>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="telephone">Téléphone</Label>
                <Input
                  id="telephone"
                  name="telephone"
                  value={formData.telephone}
                  onChange={handleChange}
                  placeholder="06 12 34 56 78"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  value={formData.email}
                  onChange={handleChange}
                  placeholder="<EMAIL>"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="adresse_facturation">Adresse de facturation</Label>
              <Textarea
                id="adresse_facturation"
                name="adresse_facturation"
                value={formData.adresse_facturation}
                onChange={handleChange}
                placeholder="123 rue des Fleurs, 75001 Paris"
                rows={2}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="adresse_chantier">Adresse du chantier (si différente)</Label>
              <Textarea
                id="adresse_chantier"
                name="adresse_chantier"
                value={formData.adresse_chantier}
                onChange={handleChange}
                placeholder="456 avenue des Travaux, 75002 Paris"
                rows={2}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="notes">Notes</Label>
              <Textarea
                id="notes"
                name="notes"
                value={formData.notes}
                onChange={handleChange}
                placeholder="Informations complémentaires sur le client..."
                rows={3}
              />
            </div>
          </CardContent>
          <CardFooter className="flex justify-between">
            <Button variant="outline" type="button" onClick={() => router.back()}>
              Annuler
            </Button>
            <Button type="submit" disabled={isLoading || isInitializing || !entrepriseId}>
              {isLoading ? "Création en cours..." : "Créer le client"}
            </Button>
          </CardFooter>
        </Card>
      </form>
    </div>
  )
}
