import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Plus, Search } from "lucide-react"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { ClientList } from "./client-list"
import Link from "next/link"

export default function ClientsPage() {
  return (
    <div className="flex flex-col gap-4">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Clients</h1>
        <Button asChild>
          <Link href="/clients/nouveau">
            <Plus className="mr-2 h-4 w-4" />
            Nouveau client
          </Link>
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Liste des clients</CardTitle>
          <CardDescription>G<PERSON>rez vos clients particuliers et professionnels</CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="tous" className="space-y-4">
            <div className="flex flex-col sm:flex-row justify-between gap-4">
              <TabsList>
                <TabsTrigger value="tous">Tous</TabsTrigger>
                <TabsTrigger value="particuliers">Particuliers</TabsTrigger>
                <TabsTrigger value="professionnels">Professionnels</TabsTrigger>
              </TabsList>
              <div className="flex gap-2">
                <div className="relative">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input type="search" placeholder="Rechercher..." className="pl-8 w-[200px]" />
                </div>
                <Select>
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Trier par" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="nom-asc">Nom (A-Z)</SelectItem>
                    <SelectItem value="nom-desc">Nom (Z-A)</SelectItem>
                    <SelectItem value="projets-desc">Projets (max)</SelectItem>
                    <SelectItem value="projets-asc">Projets (min)</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <TabsContent value="tous">
              <ClientList />
            </TabsContent>
            <TabsContent value="particuliers">
              <ClientList />
            </TabsContent>
            <TabsContent value="professionnels">
              <ClientList />
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  )
}
