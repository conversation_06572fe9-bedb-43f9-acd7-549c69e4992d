import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Plus, Search } from "lucide-react"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { CatalogueList } from "./catalogue-list"
import Link from "next/link"

export default function CataloguePage() {
  return (
    <div className="flex flex-col gap-4">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Catalogue</h1>
        <Button asChild>
          <Link href="/catalogue/nouveau">
            <Plus className="mr-2 h-4 w-4" />
            Nouvel élément
          </Link>
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Bibliothèque de prix</CardTitle>
          <CardDescription>G<PERSON>rez votre catalogue de matériaux, main d'œuvre et prestations</CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="tous" className="space-y-4">
            <div className="flex flex-col sm:flex-row justify-between gap-4">
              <TabsList>
                <TabsTrigger value="tous">Tous</TabsTrigger>
                <TabsTrigger value="materiaux">Matériaux</TabsTrigger>
                <TabsTrigger value="main-doeuvre">Main d'œuvre</TabsTrigger>
                <TabsTrigger value="prestations">Prestations</TabsTrigger>
              </TabsList>
              <div className="flex gap-2">
                <div className="relative">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input type="search" placeholder="Rechercher..." className="pl-8 w-[200px]" />
                </div>
                <Select>
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Catégorie" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="maconnerie">Maçonnerie</SelectItem>
                    <SelectItem value="platrerie">Plâtrerie</SelectItem>
                    <SelectItem value="plomberie">Plomberie</SelectItem>
                    <SelectItem value="electricite">Électricité</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <TabsContent value="tous">
              <CatalogueList />
            </TabsContent>
            <TabsContent value="materiaux">
              <CatalogueList />
            </TabsContent>
            <TabsContent value="main-doeuvre">
              <CatalogueList />
            </TabsContent>
            <TabsContent value="prestations">
              <CatalogueList />
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  )
}
