"use client"

import { useEffect, useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Edit, Trash2 } from "lucide-react"
import { type CatalogueItem, getCatalogueItems } from "@/services/catalogue"
import { useToast } from "@/hooks/use-toast"

export function CatalogueList() {
  const [items, setItems] = useState<CatalogueItem[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const { toast } = useToast()

  useEffect(() => {
    async function loadItems() {
      try {
        const data = await getCatalogueItems()
        setItems(data)
      } catch (error: any) {
        toast({
          title: "Erreur",
          description: error.message || "Impossible de charger le catalogue",
          variant: "destructive",
        })
      } finally {
        setIsLoading(false)
      }
    }

    loadItems()
  }, [toast])

  if (isLoading) {
    return <div>Chargement du catalogue...</div>
  }

  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>Référence</TableHead>
          <TableHead>Nom</TableHead>
          <TableHead>Type</TableHead>
          <TableHead>Catégorie</TableHead>
          <TableHead>Unité</TableHead>
          <TableHead>Prix HT</TableHead>
          <TableHead>TVA</TableHead>
          <TableHead className="text-right">Actions</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {items.length === 0 ? (
          <TableRow>
            <TableCell colSpan={7} className="text-center">
              Aucun élément trouvé dans le catalogue
            </TableCell>
          </TableRow>
        ) : (
          items.map((item) => (
            <TableRow key={item.id}>
              <TableCell className="font-medium">{item.reference}</TableCell>
              <TableCell>{item.nom}</TableCell>
              <TableCell>
                <Badge
                  variant={
                    item.type === "matériaux" ? "outline" : item.type === "main d'œuvre" ? "secondary" : "default"
                  }
                >
                  {item.type}
                </Badge>
              </TableCell>
              <TableCell>{item.categorie}</TableCell>
              <TableCell>{item.unite}</TableCell>
              <TableCell>{item.prix_unitaire_ht.toLocaleString("fr-FR")} €</TableCell>
              <TableCell>{item.taux_tva}%</TableCell>
              <TableCell className="text-right">
                <Button variant="ghost" size="icon">
                  <Edit className="h-4 w-4" />
                  <span className="sr-only">Modifier</span>
                </Button>
                <Button variant="ghost" size="icon">
                  <Trash2 className="h-4 w-4" />
                  <span className="sr-only">Supprimer</span>
                </Button>
              </TableCell>
            </TableRow>
          ))
        )}
      </TableBody>
    </Table>
  )
}
