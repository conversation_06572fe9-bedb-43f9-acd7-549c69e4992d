"use client"

import type React from "react"

import { useState } from "react"
import { useRout<PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useToast } from "@/hooks/use-toast"
import { createCatalogueItem } from "@/services/catalogue"

export default function NouveauCataloguePage() {
  const router = useRouter()
  const { toast } = useToast()
  const [isLoading, setIsLoading] = useState(false)

  // État pour les champs du formulaire
  const [formData, setFormData] = useState({
    nom: "",
    type: "matériaux" as "matériaux" | "main d'œuvre" | "prestation",
    reference: "",
    description: "",
    prix_unitaire_ht: "",
    unite: "",
    taux_tva: "20",
    categorie: "",
    sous_categorie: "",
  })

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleSelectChange = (name: string, value: string) => {
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    try {
      // Créer un objet catalogue à partir des données du formulaire
      const catalogueData = {
        entreprise_id: 1, // À remplacer par l'ID réel de l'entreprise
        nom: formData.nom,
        type: formData.type,
        reference: formData.reference,
        description: formData.description,
        prix_unitaire_ht: Number.parseFloat(formData.prix_unitaire_ht),
        unite: formData.unite,
        taux_tva: Number.parseFloat(formData.taux_tva),
        categorie: formData.categorie,
        sous_categorie: formData.sous_categorie,
        actif: true,
      }

      await createCatalogueItem(catalogueData)

      toast({
        title: "Élément créé avec succès",
        description: "L'élément a été ajouté à votre catalogue",
      })

      router.push("/catalogue")
      router.refresh()
    } catch (error: any) {
      toast({
        title: "Erreur",
        description: error.message || "Une erreur est survenue lors de la création de l'élément",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="flex flex-col gap-4">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Nouvel élément de catalogue</h1>
      </div>

      <form onSubmit={handleSubmit}>
        <Card>
          <CardHeader>
            <CardTitle>Informations de l'élément</CardTitle>
            <CardDescription>Ajoutez un nouvel élément à votre catalogue</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="nom">Nom</Label>
                <Input
                  id="nom"
                  name="nom"
                  value={formData.nom}
                  onChange={handleChange}
                  placeholder="Ciment Portland"
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="reference">Référence</Label>
                <Input
                  id="reference"
                  name="reference"
                  value={formData.reference}
                  onChange={handleChange}
                  placeholder="MAT-001"
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="type">Type</Label>
                <Select value={formData.type} onValueChange={(value) => handleSelectChange("type", value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Sélectionner un type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="matériaux">Matériaux</SelectItem>
                    <SelectItem value="main d'œuvre">Main d'œuvre</SelectItem>
                    <SelectItem value="prestation">Prestation</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="unite">Unité</Label>
                <Select value={formData.unite} onValueChange={(value) => handleSelectChange("unite", value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Sélectionner une unité" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="unité">Unité</SelectItem>
                    <SelectItem value="m²">m²</SelectItem>
                    <SelectItem value="m">m</SelectItem>
                    <SelectItem value="kg">kg</SelectItem>
                    <SelectItem value="sac">Sac</SelectItem>
                    <SelectItem value="heure">Heure</SelectItem>
                    <SelectItem value="forfait">Forfait</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="prix_unitaire_ht">Prix unitaire HT</Label>
                <Input
                  id="prix_unitaire_ht"
                  name="prix_unitaire_ht"
                  type="number"
                  step="0.01"
                  value={formData.prix_unitaire_ht}
                  onChange={handleChange}
                  placeholder="10.50"
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="taux_tva">Taux de TVA (%)</Label>
                <Select value={formData.taux_tva} onValueChange={(value) => handleSelectChange("taux_tva", value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Sélectionner un taux" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="20">20%</SelectItem>
                    <SelectItem value="10">10%</SelectItem>
                    <SelectItem value="5.5">5.5%</SelectItem>
                    <SelectItem value="0">0%</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="categorie">Catégorie</Label>
                <Select value={formData.categorie} onValueChange={(value) => handleSelectChange("categorie", value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Sélectionner une catégorie" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Maçonnerie">Maçonnerie</SelectItem>
                    <SelectItem value="Plâtrerie">Plâtrerie</SelectItem>
                    <SelectItem value="Plomberie">Plomberie</SelectItem>
                    <SelectItem value="Électricité">Électricité</SelectItem>
                    <SelectItem value="Menuiserie">Menuiserie</SelectItem>
                    <SelectItem value="Peinture">Peinture</SelectItem>
                    <SelectItem value="Carrelage">Carrelage</SelectItem>
                    <SelectItem value="Autre">Autre</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="sous_categorie">Sous-catégorie</Label>
                <Input
                  id="sous_categorie"
                  name="sous_categorie"
                  value={formData.sous_categorie}
                  onChange={handleChange}
                  placeholder="Optionnel"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                name="description"
                value={formData.description}
                onChange={handleChange}
                placeholder="Description détaillée de l'élément..."
                rows={3}
              />
            </div>
          </CardContent>
          <CardFooter className="flex justify-between">
            <Button variant="outline" type="button" onClick={() => router.back()}>
              Annuler
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? "Création en cours..." : "Créer l'élément"}
            </Button>
          </CardFooter>
        </Card>
      </form>
    </div>
  )
}
