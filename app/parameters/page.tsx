import type { Metada<PERSON> } from "next"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import ProfileSettings from "./profile-settings"
import EnterpriseSettings from "./enterprise-settings"
import ApplicationSettings from "./application-settings"

export const metadata: Metadata = {
  title: "Paramètres | BTP Manager",
  description: "Gérez les paramètres de votre compte et de votre entreprise",
}

export default function ParametersPage() {
  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Paramètres</h1>
          <p className="text-muted-foreground">Gérez les paramètres de votre compte et de votre entreprise</p>
        </div>
      </div>

      <Tabs defaultValue="profile" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="profile">Profil</TabsTrigger>
          <TabsTrigger value="enterprise">Entreprise</TabsTrigger>
          <TabsTrigger value="application">Application</TabsTrigger>
        </TabsList>
        <TabsContent value="profile">
          <Card>
            <CardHeader>
              <CardTitle>Paramètres du profil</CardTitle>
              <CardDescription>Gérez vos informations personnelles et vos préférences de compte</CardDescription>
            </CardHeader>
            <CardContent>
              <ProfileSettings />
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="enterprise">
          <Card>
            <CardHeader>
              <CardTitle>Paramètres de l&apos;entreprise</CardTitle>
              <CardDescription>
                Gérez les informations de votre entreprise et les paramètres commerciaux
              </CardDescription>
            </CardHeader>
            <CardContent>
              <EnterpriseSettings />
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="application">
          <Card>
            <CardHeader>
              <CardTitle>Paramètres de l&apos;application</CardTitle>
              <CardDescription>Personnalisez l&apos;application selon vos préférences</CardDescription>
            </CardHeader>
            <CardContent>
              <ApplicationSettings />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
