"use client"

import type React from "react"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { useToast } from "@/hooks/use-toast"
import { isDemoMode } from "@/lib/demo-mode"

export default function EnterpriseSettings() {
  const { toast } = useToast()
  const [isLoading, setIsLoading] = useState(false)
  const [formData, setFormData] = useState({
    nom: "BTP Expert",
    siret: "12345678901234",
    adresse: "123 rue des Artisans",
    codePostal: "75001",
    ville: "Paris",
    telephone: "01 23 45 67 89",
    email: "<EMAIL>",
    siteWeb: "www.btpexpert.fr",
    logo: "",
    cgv: "Conditions générales de vente...",
  })

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (isDemoMode()) {
      toast({
        title: "Mode démo",
        description: "Les modifications ne sont pas enregistrées en mode démo",
      })
      return
    }

    setIsLoading(true)

    try {
      // Simuler une requête API
      await new Promise((resolve) => setTimeout(resolve, 1000))

      toast({
        title: "Entreprise mise à jour",
        description: "Les informations de votre entreprise ont été mises à jour avec succès",
      })
    } catch (error) {
      console.error("Erreur lors de la mise à jour de l'entreprise:", error)
      toast({
        title: "Erreur",
        description: "Une erreur est survenue lors de la mise à jour de l'entreprise",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="space-y-2">
        <Label htmlFor="nom">Nom de l&apos;entreprise</Label>
        <Input id="nom" name="nom" value={formData.nom} onChange={handleChange} required />
      </div>

      <div className="space-y-2">
        <Label htmlFor="siret">SIRET</Label>
        <Input id="siret" name="siret" value={formData.siret} onChange={handleChange} required />
      </div>

      <div className="space-y-2">
        <Label htmlFor="adresse">Adresse</Label>
        <Input id="adresse" name="adresse" value={formData.adresse} onChange={handleChange} required />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="codePostal">Code postal</Label>
          <Input id="codePostal" name="codePostal" value={formData.codePostal} onChange={handleChange} required />
        </div>
        <div className="space-y-2">
          <Label htmlFor="ville">Ville</Label>
          <Input id="ville" name="ville" value={formData.ville} onChange={handleChange} required />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="telephone">Téléphone</Label>
          <Input id="telephone" name="telephone" value={formData.telephone} onChange={handleChange} />
        </div>
        <div className="space-y-2">
          <Label htmlFor="email">Email</Label>
          <Input id="email" name="email" type="email" value={formData.email} onChange={handleChange} />
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="siteWeb">Site web</Label>
        <Input id="siteWeb" name="siteWeb" value={formData.siteWeb} onChange={handleChange} />
      </div>

      <div className="space-y-2">
        <Label htmlFor="logo">Logo</Label>
        <Input id="logo" name="logo" type="file" accept="image/*" className="cursor-pointer" />
      </div>

      <div className="space-y-2">
        <Label htmlFor="cgv">Conditions générales de vente</Label>
        <Textarea id="cgv" name="cgv" value={formData.cgv} onChange={handleChange} rows={6} />
      </div>

      <Button type="submit" disabled={isLoading}>
        {isLoading ? "Enregistrement..." : "Enregistrer les modifications"}
      </Button>
    </form>
  )
}
