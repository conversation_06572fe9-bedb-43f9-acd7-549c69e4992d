"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useToast } from "@/hooks/use-toast"
import { isDemoMode } from "@/lib/demo-mode"

export default function ApplicationSettings() {
  const { toast } = useToast()
  const [isLoading, setIsLoading] = useState(false)
  const [settings, setSettings] = useState({
    darkMode: false,
    notifications: true,
    emailAlerts: true,
    language: "fr",
    currency: "EUR",
  })

  const handleSwitchChange = (name: string) => {
    setSettings((prev) => ({ ...prev, [name]: !prev[name as keyof typeof prev] }))
  }

  const handleSelectChange = (name: string, value: string) => {
    setSettings((prev) => ({ ...prev, [name]: value }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (isDemoMode()) {
      toast({
        title: "Mode démo",
        description: "Les modifications ne sont pas enregistrées en mode démo",
      })
      return
    }

    setIsLoading(true)

    try {
      // Simuler une requête API
      await new Promise((resolve) => setTimeout(resolve, 1000))

      toast({
        title: "Paramètres mis à jour",
        description: "Les paramètres de l'application ont été mis à jour avec succès",
      })
    } catch (error) {
      console.error("Erreur lors de la mise à jour des paramètres:", error)
      toast({
        title: "Erreur",
        description: "Une erreur est survenue lors de la mise à jour des paramètres",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="space-y-0.5">
          <Label htmlFor="dark-mode">Mode sombre</Label>
          <p className="text-sm text-muted-foreground">Activer le thème sombre pour l&apos;application</p>
        </div>
        <Switch id="dark-mode" checked={settings.darkMode} onCheckedChange={() => handleSwitchChange("darkMode")} />
      </div>

      <div className="flex items-center justify-between">
        <div className="space-y-0.5">
          <Label htmlFor="notifications">Notifications dans l&apos;application</Label>
          <p className="text-sm text-muted-foreground">Recevoir des notifications dans l&apos;application</p>
        </div>
        <Switch
          id="notifications"
          checked={settings.notifications}
          onCheckedChange={() => handleSwitchChange("notifications")}
        />
      </div>

      <div className="flex items-center justify-between">
        <div className="space-y-0.5">
          <Label htmlFor="email-alerts">Alertes par email</Label>
          <p className="text-sm text-muted-foreground">Recevoir des alertes par email pour les événements importants</p>
        </div>
        <Switch
          id="email-alerts"
          checked={settings.emailAlerts}
          onCheckedChange={() => handleSwitchChange("emailAlerts")}
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="language">Langue</Label>
        <Select value={settings.language} onValueChange={(value) => handleSelectChange("language", value)}>
          <SelectTrigger id="language" className="w-full">
            <SelectValue placeholder="Sélectionner une langue" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="fr">Français</SelectItem>
            <SelectItem value="en">English</SelectItem>
            <SelectItem value="es">Español</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-2">
        <Label htmlFor="currency">Devise</Label>
        <Select value={settings.currency} onValueChange={(value) => handleSelectChange("currency", value)}>
          <SelectTrigger id="currency" className="w-full">
            <SelectValue placeholder="Sélectionner une devise" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="EUR">Euro (€)</SelectItem>
            <SelectItem value="USD">Dollar américain ($)</SelectItem>
            <SelectItem value="GBP">Livre sterling (£)</SelectItem>
            <SelectItem value="CHF">Franc suisse (CHF)</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <Button type="submit" disabled={isLoading}>
        {isLoading ? "Enregistrement..." : "Enregistrer les paramètres"}
      </Button>
    </form>
  )
}
