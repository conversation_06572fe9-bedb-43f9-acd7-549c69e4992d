"use client"

import type React from "react"

import { Inter } from "next/font/google"
import "./globals.css"
import { ThemeProvider } from "@/components/theme-provider"
import { Toaster } from "@/components/ui/toaster"
import { usePathname } from "next/navigation"
import Sidebar from "@/components/sidebar"
import Header from "@/components/header"
import { EntrepriseCheck } from "@/components/entreprise-check"
import { DemoModeBanner } from "@/components/demo-mode-banner"
import { DatabaseInitScript } from "@/components/database-init-script"

const inter = Inter({ subsets: ["latin"] })

export default function ClientLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const pathname = usePathname()
  const isAuthPage = pathname === "/login" || pathname === "/register" || pathname.startsWith("/auth")
  const isAdminPage = pathname.startsWith("/admin")
  const skipEntrepriseCheck = isAuthPage || isAdminPage

  // Ensure children is not a function
  const renderChildren = typeof children === "function" ? children() : children

  return (
    <html lang="fr" suppressHydrationWarning>
      <body className={inter.className}>
        <ThemeProvider attribute="class" defaultTheme="light" enableSystem disableTransitionOnChange>
          <DatabaseInitScript />
          {skipEntrepriseCheck ? (
            <div className={isAdminPage ? "min-h-screen p-6" : "min-h-screen"}>
              {!isAuthPage && <DemoModeBanner />}
              {renderChildren}
            </div>
          ) : (
            <EntrepriseCheck>
              <div className="flex min-h-screen flex-col">
                <Header />
                <div className="flex flex-1">
                  <Sidebar />
                  <main className="flex-1 p-6">
                    <DemoModeBanner />
                    {renderChildren}
                  </main>
                </div>
              </div>
            </EntrepriseCheck>
          )}
          <Toaster />
        </ThemeProvider>
      </body>
    </html>
  )
}
