"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Ta<PERSON>, <PERSON>bsContent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import { CalendarIcon, Plus, Search } from "lucide-react"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { FactureList } from "./facture-list"
import Link from "next/link"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Calendar } from "@/components/ui/calendar"
import { fr } from "date-fns/locale"

export default function FacturesPage() {
  return (
    <div className="flex flex-col gap-4">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Factures</h1>
        <Button asChild>
          <Link href="/factures/nouveau">
            <Plus className="mr-2 h-4 w-4" />
            Nouvelle facture
          </Link>
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Liste des factures</CardTitle>
          <CardDescription>Gérez toutes vos factures et suivez leur statut</CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="tous" className="space-y-4">
            <div className="flex flex-col sm:flex-row justify-between gap-4">
              <TabsList>
                <TabsTrigger value="tous">Toutes</TabsTrigger>
                <TabsTrigger value="brouillons">Brouillons</TabsTrigger>
                <TabsTrigger value="envoyees">Envoyées</TabsTrigger>
                <TabsTrigger value="payees">Payées</TabsTrigger>
                <TabsTrigger value="impayees">Impayées</TabsTrigger>
              </TabsList>
              <div className="flex gap-2">
                <div className="relative">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input type="search" placeholder="Rechercher..." className="pl-8 w-[200px]" />
                </div>
                <Select>
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Trier par" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="date-desc">Date (récent)</SelectItem>
                    <SelectItem value="date-asc">Date (ancien)</SelectItem>
                    <SelectItem value="montant-desc">Montant (élevé)</SelectItem>
                    <SelectItem value="montant-asc">Montant (faible)</SelectItem>
                  </SelectContent>
                </Select>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button variant="outline" className="flex items-center gap-2">
                      <CalendarIcon className="h-4 w-4" />
                      Date
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar mode="single" locale={fr} />
                  </PopoverContent>
                </Popover>
              </div>
            </div>
            <TabsContent value="tous">
              <FactureList />
            </TabsContent>
            <TabsContent value="brouillons">
              <FactureList statut="brouillon" />
            </TabsContent>
            <TabsContent value="envoyees">
              <FactureList statut="envoyée" />
            </TabsContent>
            <TabsContent value="payees">
              <FactureList statut="payée" />
            </TabsContent>
            <TabsContent value="impayees">
              <FactureList statut="impayée" />
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  )
}
