"use client"

import { useEffect, useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Download, Eye } from "lucide-react"
import { type Facture, getFactures } from "@/services/factures"
import { useToast } from "@/hooks/use-toast"
import { Card, CardContent } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"

interface FactureListProps {
  statut?: "brouillon" | "envoyée" | "payée" | "impayée"
}

export function FactureList({ statut }: FactureListProps) {
  const [factures, setFactures] = useState<Facture[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const { toast } = useToast()

  useEffect(() => {
    async function loadFactures() {
      try {
        setIsLoading(true)
        setError(null)
        const data = await getFactures()
        setFactures(statut ? data.filter((facture) => facture.statut === statut) : data)
      } catch (error: any) {
        setError(error.message || "Impossible de charger les factures")
        toast({
          title: "Erreur",
          description: error.message || "Impossible de charger les factures",
          variant: "destructive",
        })
      } finally {
        setIsLoading(false)
      }
    }

    loadFactures()
  }, [statut, toast])

  if (isLoading) {
    return (
      <div className="space-y-3">
        {Array(5)
          .fill(0)
          .map((_, index) => (
            <Card key={index}>
              <CardContent className="p-4">
                <div className="flex justify-between items-center">
                  <div className="space-y-2">
                    <Skeleton className="h-4 w-[250px]" />
                    <Skeleton className="h-4 w-[200px]" />
                  </div>
                  <Skeleton className="h-8 w-8 rounded-full" />
                </div>
              </CardContent>
            </Card>
          ))}
      </div>
    )
  }

  if (error) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <p className="text-red-500 mb-4">{error}</p>
          <Button onClick={() => window.location.reload()}>Réessayer</Button>
        </CardContent>
      </Card>
    )
  }

  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>Référence</TableHead>
          <TableHead>Client</TableHead>
          <TableHead>Date</TableHead>
          <TableHead>Échéance</TableHead>
          <TableHead>Type</TableHead>
          <TableHead>Montant TTC</TableHead>
          <TableHead>Statut</TableHead>
          <TableHead className="text-right">Actions</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {factures.length === 0 ? (
          <TableRow>
            <TableCell colSpan={8} className="text-center">
              Aucune facture trouvée
            </TableCell>
          </TableRow>
        ) : (
          factures.map((facture) => (
            <TableRow key={facture.id}>
              <TableCell className="font-medium">{facture.reference}</TableCell>
              <TableCell>{facture.client_nom}</TableCell>
              <TableCell>{new Date(facture.date_emission).toLocaleDateString("fr-FR")}</TableCell>
              <TableCell>{new Date(facture.date_echeance).toLocaleDateString("fr-FR")}</TableCell>
              <TableCell>
                <Badge variant="outline">
                  {facture.type === "acompte" ? "Acompte" : facture.type === "situation" ? "Situation" : "Solde"}
                </Badge>
              </TableCell>
              <TableCell>{facture.montant_ttc.toLocaleString("fr-FR")} €</TableCell>
              <TableCell>
                <Badge
                  variant={
                    facture.statut === "payée"
                      ? "success"
                      : facture.statut === "envoyée"
                        ? "default"
                        : facture.statut === "impayée"
                          ? "destructive"
                          : "outline"
                  }
                >
                  {facture.statut}
                </Badge>
              </TableCell>
              <TableCell className="text-right">
                <Button variant="ghost" size="icon">
                  <Eye className="h-4 w-4" />
                  <span className="sr-only">Voir la facture</span>
                </Button>
                <Button variant="ghost" size="icon">
                  <Download className="h-4 w-4" />
                  <span className="sr-only">Télécharger</span>
                </Button>
              </TableCell>
            </TableRow>
          ))
        )}
      </TableBody>
    </Table>
  )
}
