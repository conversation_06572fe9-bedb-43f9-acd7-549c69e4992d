"use client"

import type { ChangeEvent, FormEvent } from "react"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Calendar } from "@/components/ui/calendar"
import { fr } from "date-fns/locale"
import { format } from "date-fns"
import { CalendarIcon } from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { createFacture } from "@/services/factures"
import { getDevis } from "@/services/devis"

export default function NouvelleFacturePage() {
  const router = useRouter()
  const { toast } = useToast()
  const [isLoading, setIsLoading] = useState(false)
  const [devisList, setDevisList] = useState([])
  const [dateEmission, setDateEmission] = useState<Date>(new Date())
  const [dateEcheance, setDateEcheance] = useState<Date>(new Date(Date.now() + 30 * 24 * 60 * 60 * 1000))

  // État pour les champs du formulaire
  const [formData, setFormData] = useState({
    reference: `FACT-${new Date().getFullYear()}-${String(Math.floor(Math.random() * 1000)).padStart(3, "0")}`,
    devis_id: "",
    type: "acompte",
    montant_ht: "",
    montant_ttc: "",
    statut: "brouillon",
    mode_paiement: "",
  })

  // Charger les devis au chargement de la page
  useEffect(() => {
    async function loadDevis() {
      try {
        const devisData = await getDevis()
        setDevisList(devisData)
      } catch (error) {
        console.error("Erreur lors du chargement des devis:", error)
        toast({
          title: "Erreur",
          description: "Impossible de charger la liste des devis",
          variant: "destructive",
        })
      }
    }

    loadDevis()
  }, [toast])

  const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleSelectChange = (name: string, value: string) => {
    setFormData((prev) => ({ ...prev, [name]: value }))

    // Si on sélectionne un devis, on peut pré-remplir les montants
    if (name === "devis_id" && value) {
      const selectedDevis = devisList.find((d) => d.id.toString() === value)
      if (selectedDevis) {
        // Pour un acompte, on prend 30% du montant total
        if (formData.type === "acompte") {
          const montantHT = selectedDevis.montant_ht * 0.3
          const montantTTC = selectedDevis.montant_ttc * 0.3
          setFormData((prev) => ({
            ...prev,
            montant_ht: montantHT.toFixed(2),
            montant_ttc: montantTTC.toFixed(2),
          }))
        } else {
          // Pour un solde, on prend le montant total
          setFormData((prev) => ({
            ...prev,
            montant_ht: selectedDevis.montant_ht.toString(),
            montant_ttc: selectedDevis.montant_ttc.toString(),
          }))
        }
      }
    }

    // Si on change le type de facture, on ajuste les montants si un devis est sélectionné
    if (name === "type" && formData.devis_id) {
      const selectedDevis = devisList.find((d) => d.id.toString() === formData.devis_id)
      if (selectedDevis) {
        if (value === "acompte") {
          const montantHT = selectedDevis.montant_ht * 0.3
          const montantTTC = selectedDevis.montant_ttc * 0.3
          setFormData((prev) => ({
            ...prev,
            montant_ht: montantHT.toFixed(2),
            montant_ttc: montantTTC.toFixed(2),
          }))
        } else if (value === "solde") {
          setFormData((prev) => ({
            ...prev,
            montant_ht: selectedDevis.montant_ht.toString(),
            montant_ttc: selectedDevis.montant_ttc.toString(),
          }))
        }
      }
    }
  }

  // Calculer le montant TTC à partir du montant HT
  const calculateTTC = (ht: string) => {
    const htValue = Number.parseFloat(ht)
    if (!isNaN(htValue)) {
      // On applique un taux de TVA de 20% par défaut
      const ttc = htValue * 1.2
      setFormData((prev) => ({ ...prev, montant_ttc: ttc.toFixed(2) }))
    }
  }

  // Mettre à jour le montant TTC quand le montant HT change
  useEffect(() => {
    if (formData.montant_ht) {
      calculateTTC(formData.montant_ht)
    }
  }, [formData.montant_ht])

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    try {
      // Créer un objet facture à partir des données du formulaire
      const factureData = {
        devis_id: formData.devis_id ? Number.parseInt(formData.devis_id) : null,
        reference: formData.reference,
        type: formData.type as "acompte" | "situation" | "solde",
        date_emission: dateEmission.toISOString(),
        date_echeance: dateEcheance.toISOString(),
        montant_ht: Number.parseFloat(formData.montant_ht),
        montant_ttc: Number.parseFloat(formData.montant_ttc),
        statut: formData.statut as "brouillon" | "envoyée" | "payée" | "impayée",
        date_paiement: null,
        mode_paiement: formData.mode_paiement || null,
      }

      await createFacture(factureData)

      toast({
        title: "Facture créée avec succès",
        description: "La facture a été ajoutée à votre base de données",
      })

      router.push("/factures")
      router.refresh()
    } catch (error: any) {
      toast({
        title: "Erreur",
        description: error.message || "Une erreur est survenue lors de la création de la facture",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="flex flex-col gap-4">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Nouvelle facture</h1>
      </div>

      <form onSubmit={handleSubmit}>
        <Card>
          <CardHeader>
            <CardTitle>Informations de la facture</CardTitle>
            <CardDescription>Ajoutez une nouvelle facture à votre base de données</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="reference">Référence</Label>
                <Input
                  id="reference"
                  name="reference"
                  value={formData.reference}
                  onChange={handleChange}
                  placeholder="FACT-2023-001"
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="devis_id">Devis associé</Label>
                <Select value={formData.devis_id} onValueChange={(value) => handleSelectChange("devis_id", value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Sélectionner un devis (optionnel)" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="aucun">Aucun devis associé</SelectItem>
                    {devisList.map((devis) => (
                      <SelectItem key={devis.id} value={devis.id.toString()}>
                        {devis.reference} - {devis.montant_ttc.toLocaleString("fr-FR")} €
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="type">Type de facture</Label>
                <Select value={formData.type} onValueChange={(value) => handleSelectChange("type", value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Sélectionner un type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="acompte">Acompte</SelectItem>
                    <SelectItem value="situation">Situation</SelectItem>
                    <SelectItem value="solde">Solde</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="statut">Statut</Label>
                <Select value={formData.statut} onValueChange={(value) => handleSelectChange("statut", value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Sélectionner un statut" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="brouillon">Brouillon</SelectItem>
                    <SelectItem value="envoyée">Envoyée</SelectItem>
                    <SelectItem value="payée">Payée</SelectItem>
                    <SelectItem value="impayée">Impayée</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="date_emission">Date d'émission</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button variant="outline" className="w-full justify-start text-left font-normal" id="date_emission">
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {dateEmission ? format(dateEmission, "PPP", { locale: fr }) : "Sélectionner une date"}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={dateEmission}
                      onSelect={(date) => date && setDateEmission(date)}
                      locale={fr}
                    />
                  </PopoverContent>
                </Popover>
              </div>
              <div className="space-y-2">
                <Label htmlFor="date_echeance">Date d'échéance</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button variant="outline" className="w-full justify-start text-left font-normal" id="date_echeance">
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {dateEcheance ? format(dateEcheance, "PPP", { locale: fr }) : "Sélectionner une date"}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={dateEcheance}
                      onSelect={(date) => date && setDateEcheance(date)}
                      locale={fr}
                    />
                  </PopoverContent>
                </Popover>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="montant_ht">Montant HT</Label>
                <Input
                  id="montant_ht"
                  name="montant_ht"
                  type="number"
                  step="0.01"
                  value={formData.montant_ht}
                  onChange={handleChange}
                  placeholder="1000.00"
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="montant_ttc">Montant TTC</Label>
                <Input
                  id="montant_ttc"
                  name="montant_ttc"
                  type="number"
                  step="0.01"
                  value={formData.montant_ttc}
                  onChange={handleChange}
                  placeholder="1200.00"
                  required
                />
              </div>
            </div>

            {formData.statut === "payée" && (
              <div className="space-y-2">
                <Label htmlFor="mode_paiement">Mode de paiement</Label>
                <Select
                  value={formData.mode_paiement}
                  onValueChange={(value) => handleSelectChange("mode_paiement", value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Sélectionner un mode de paiement" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="virement">Virement bancaire</SelectItem>
                    <SelectItem value="cheque">Chèque</SelectItem>
                    <SelectItem value="especes">Espèces</SelectItem>
                    <SelectItem value="cb">Carte bancaire</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            )}
          </CardContent>
          <CardFooter className="flex justify-between">
            <Button variant="outline" type="button" onClick={() => router.back()}>
              Annuler
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? "Création en cours..." : "Créer la facture"}
            </Button>
          </CardFooter>
        </Card>
      </form>
    </div>
  )
}
