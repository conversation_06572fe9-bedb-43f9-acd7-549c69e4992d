"use client"

import { useEffect, useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Eye } from "lucide-react"
import { type Projet, getProjets } from "@/services/projets"
import { useToast } from "@/hooks/use-toast"
import { Card, CardContent } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"

interface ProjetListProps {
  statut?: "devis" | "signé" | "en_cours" | "terminé" | "facturé"
}

export function ProjetList({ statut }: ProjetListProps) {
  const [projets, setProjets] = useState<Projet[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const { toast } = useToast()

  useEffect(() => {
    async function loadProjets() {
      try {
        setIsLoading(true)
        setError(null)
        const data = await getProjets()
        setProjets(statut ? data.filter((projet) => projet.statut === statut) : data)
      } catch (error: any) {
        setError(error.message || "Impossible de charger les projets")
        toast({
          title: "Erreur",
          description: error.message || "Impossible de charger les projets",
          variant: "destructive",
        })
      } finally {
        setIsLoading(false)
      }
    }

    loadProjets()
  }, [statut, toast])

  if (isLoading) {
    return (
      <div className="space-y-3">
        {Array(5)
          .fill(0)
          .map((_, index) => (
            <Card key={index}>
              <CardContent className="p-4">
                <div className="flex justify-between items-center">
                  <div className="space-y-2">
                    <Skeleton className="h-4 w-[250px]" />
                    <Skeleton className="h-4 w-[200px]" />
                  </div>
                  <Skeleton className="h-8 w-8 rounded-full" />
                </div>
              </CardContent>
            </Card>
          ))}
      </div>
    )
  }

  if (error) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <p className="text-red-500 mb-4">{error}</p>
          <Button onClick={() => window.location.reload()}>Réessayer</Button>
        </CardContent>
      </Card>
    )
  }

  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>Nom</TableHead>
          <TableHead>Client</TableHead>
          <TableHead>Date de création</TableHead>
          <TableHead>Statut</TableHead>
          <TableHead className="text-right">Actions</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {projets.length === 0 ? (
          <TableRow>
            <TableCell colSpan={5} className="text-center">
              Aucun projet trouvé
            </TableCell>
          </TableRow>
        ) : (
          projets.map((projet) => (
            <TableRow key={projet.id}>
              <TableCell className="font-medium">{projet.nom}</TableCell>
              <TableCell>
                {projet.client
                  ? projet.client.type === "particulier"
                    ? `${projet.client.nom} ${projet.client.prenom}`
                    : projet.client.societe
                  : "N/A"}
              </TableCell>
              <TableCell>{new Date(projet.date_creation).toLocaleDateString("fr-FR")}</TableCell>
              <TableCell>
                <Badge
                  variant={
                    projet.statut === "signé"
                      ? "success"
                      : projet.statut === "en_cours"
                        ? "default"
                        : projet.statut === "terminé"
                          ? "secondary"
                          : "outline"
                  }
                >
                  {projet.statut}
                </Badge>
              </TableCell>
              <TableCell className="text-right">
                <Button variant="ghost" size="icon">
                  <Eye className="h-4 w-4" />
                  <span className="sr-only">Voir le projet</span>
                </Button>
              </TableCell>
            </TableRow>
          ))
        )}
      </TableBody>
    </Table>
  )
}
