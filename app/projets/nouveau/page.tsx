"use client"

import type { ChangeEvent, FormEvent } from "react"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Calendar } from "@/components/ui/calendar"
import { fr } from "date-fns/locale"
import { format } from "date-fns"
import { CalendarIcon } from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { createProjet } from "@/services/projets"
import { getClients } from "@/services/clients"

export default function NouveauProjetPage() {
  const router = useRouter()
  const { toast } = useToast()
  const [isLoading, setIsLoading] = useState(false)
  const [clients, setClients] = useState([])
  const [dateDebutPrevu, setDateDebutPrevu] = useState<Date | undefined>(undefined)
  const [dateFinPrevu, setDateFinPrevu] = useState<Date | undefined>(undefined)

  // État pour les champs du formulaire
  const [formData, setFormData] = useState({
    reference: `PROJ-${new Date().getFullYear()}-${String(Math.floor(Math.random() * 1000)).padStart(3, "0")}`,
    nom: "",
    client_id: "",
    adresse_chantier: "",
    description: "",
    statut: "devis",
  })

  // Charger les clients au chargement de la page
  useEffect(() => {
    async function loadClients() {
      try {
        const clientsData = await getClients()
        setClients(clientsData)
      } catch (error) {
        console.error("Erreur lors du chargement des clients:", error)
        toast({
          title: "Erreur",
          description: "Impossible de charger la liste des clients",
          variant: "destructive",
        })
      }
    }

    loadClients()
  }, [toast])

  const handleChange = (e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleSelectChange = (name: string, value: string) => {
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    try {
      // Créer un objet projet à partir des données du formulaire
      const projetData = {
        client_id: Number.parseInt(formData.client_id),
        utilisateur_id: 1, // À remplacer par l'ID réel de l'utilisateur connecté
        reference: formData.reference,
        nom: formData.nom,
        adresse_chantier: formData.adresse_chantier,
        description: formData.description,
        statut: formData.statut as "devis" | "signé" | "en_cours" | "terminé" | "facturé",
        date_debut_prevu: dateDebutPrevu ? dateDebutPrevu.toISOString() : null,
        date_fin_prevu: dateFinPrevu ? dateFinPrevu.toISOString() : null,
        date_debut_reel: null,
        date_fin_reel: null,
        montant_total_ht: null,
        montant_total_ttc: null,
      }

      await createProjet(projetData)

      toast({
        title: "Projet créé avec succès",
        description: "Le projet a été ajouté à votre base de données",
      })

      router.push("/projets")
      router.refresh()
    } catch (error: any) {
      toast({
        title: "Erreur",
        description: error.message || "Une erreur est survenue lors de la création du projet",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="flex flex-col gap-4">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Nouveau projet</h1>
      </div>

      <form onSubmit={handleSubmit}>
        <Card>
          <CardHeader>
            <CardTitle>Informations du projet</CardTitle>
            <CardDescription>Ajoutez un nouveau projet à votre base de données</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="reference">Référence</Label>
                <Input
                  id="reference"
                  name="reference"
                  value={formData.reference}
                  onChange={handleChange}
                  placeholder="PROJ-2023-001"
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="client_id">Client</Label>
                <Select value={formData.client_id} onValueChange={(value) => handleSelectChange("client_id", value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Sélectionner un client" />
                  </SelectTrigger>
                  <SelectContent>
                    {clients.map((client) => (
                      <SelectItem key={client.id} value={client.id.toString()}>
                        {client.type === "particulier" ? `${client.nom} ${client.prenom}` : client.societe}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="nom">Nom du projet</Label>
              <Input
                id="nom"
                name="nom"
                value={formData.nom}
                onChange={handleChange}
                placeholder="Rénovation salle de bain"
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="adresse_chantier">Adresse du chantier</Label>
              <Textarea
                id="adresse_chantier"
                name="adresse_chantier"
                value={formData.adresse_chantier}
                onChange={handleChange}
                placeholder="123 rue des Travaux, 75001 Paris"
                rows={2}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="date_debut_prevu">Date de début prévue</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className="w-full justify-start text-left font-normal"
                      id="date_debut_prevu"
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {dateDebutPrevu ? format(dateDebutPrevu, "PPP", { locale: fr }) : "Sélectionner une date"}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar mode="single" selected={dateDebutPrevu} onSelect={setDateDebutPrevu} locale={fr} />
                  </PopoverContent>
                </Popover>
              </div>
              <div className="space-y-2">
                <Label htmlFor="date_fin_prevu">Date de fin prévue</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className="w-full justify-start text-left font-normal"
                      id="date_fin_prevu"
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {dateFinPrevu ? format(dateFinPrevu, "PPP", { locale: fr }) : "Sélectionner une date"}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar mode="single" selected={dateFinPrevu} onSelect={setDateFinPrevu} locale={fr} />
                  </PopoverContent>
                </Popover>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="statut">Statut</Label>
              <Select value={formData.statut} onValueChange={(value) => handleSelectChange("statut", value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Sélectionner un statut" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="devis">En devis</SelectItem>
                  <SelectItem value="signé">Signé</SelectItem>
                  <SelectItem value="en_cours">En cours</SelectItem>
                  <SelectItem value="terminé">Terminé</SelectItem>
                  <SelectItem value="facturé">Facturé</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                name="description"
                value={formData.description}
                onChange={handleChange}
                placeholder="Description détaillée du projet..."
                rows={3}
              />
            </div>
          </CardContent>
          <CardFooter className="flex justify-between">
            <Button variant="outline" type="button" onClick={() => router.back()}>
              Annuler
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? "Création en cours..." : "Créer le projet"}
            </Button>
          </CardFooter>
        </Card>
      </form>
    </div>
  )
}
