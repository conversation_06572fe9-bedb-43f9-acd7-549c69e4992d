import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Plus, Search } from "lucide-react"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import Link from "next/link"
import { RequestList } from "./request-list"

export default function DemandesPage() {
  return (
    <div className="flex flex-col gap-4">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Demandes clients</h1>
        <Button asChild>
          <Link href="/demandes/nouvelle">
            <Plus className="mr-2 h-4 w-4" />
            Nouvelle demande
          </Link>
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Liste des demandes</CardTitle>
          <CardDescription><PERSON><PERSON><PERSON> les demandes de vos clients</CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="tous" className="space-y-4">
            <div className="flex flex-col sm:flex-row justify-between gap-4">
              <TabsList>
                <TabsTrigger value="tous">Tous</TabsTrigger>
                <TabsTrigger value="nouveau">Nouveau</TabsTrigger>
                <TabsTrigger value="en_analyse">En analyse</TabsTrigger>
                <TabsTrigger value="accepte">Accepté</TabsTrigger>
              </TabsList>
              <div className="flex gap-2">
                <div className="relative">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input type="search" placeholder="Rechercher..." className="pl-8 w-[200px]" />
                </div>
                <Select>
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Trier par" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="date-desc">Date (récent)</SelectItem>
                    <SelectItem value="date-asc">Date (ancien)</SelectItem>
                    <SelectItem value="client-asc">Client (A-Z)</SelectItem>
                    <SelectItem value="client-desc">Client (Z-A)</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <TabsContent value="tous">
              <RequestList filter="tous" />
            </TabsContent>
            <TabsContent value="nouveau">
              <RequestList filter="nouveau" />
            </TabsContent>
            <TabsContent value="en_analyse">
              <RequestList filter="en_analyse" />
            </TabsContent>
            <TabsContent value="accepte">
              <RequestList filter="accepté" />
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  )
}
