"use client"

import { useEffect, useState } from "react"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Skeleton } from "@/components/ui/skeleton"
import { getRequests, type Request } from "@/services/requests"
import { formatDate } from "@/lib/utils"
import Link from "next/link"
import { Eye } from "lucide-react"

interface RequestListProps {
  filter: "tous" | "nouveau" | "en_analyse" | "accepté" | "refusé" | "en_cours" | "terminé"
}

export function RequestList({ filter }: RequestListProps) {
  const [requests, setRequests] = useState<Request[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    async function loadRequests() {
      try {
        setLoading(true)
        setError(null)
        const data = await getRequests()

        // Filter requests based on the filter prop
        const filteredData = filter === "tous" ? data : data.filter((request) => request.status === filter)

        setRequests(filteredData)
      } catch (err) {
        console.error("Erreur lors du chargement des demandes:", err)
        setError("Impossible de charger les demandes. Veuillez réessayer plus tard.")
      } finally {
        setLoading(false)
      }
    }

    loadRequests()
  }, [filter])

  if (loading) {
    return (
      <div className="space-y-4">
        {[...Array(3)].map((_, i) => (
          <div key={i} className="flex items-center justify-between rounded-lg border p-4">
            <div className="space-y-2">
              <Skeleton className="h-4 w-[250px]" />
              <Skeleton className="h-4 w-[200px]" />
            </div>
            <div className="flex items-center gap-4">
              <div className="text-right">
                <Skeleton className="h-4 w-[100px]" />
                <Skeleton className="h-4 w-[80px]" />
              </div>
              <Skeleton className="h-8 w-[80px]" />
            </div>
          </div>
        ))}
      </div>
    )
  }

  if (error) {
    return <div className="p-4 text-red-500">{error}</div>
  }

  if (requests.length === 0) {
    return <div className="p-4 text-muted-foreground">Aucune demande trouvée.</div>
  }

  function getStatusBadgeVariant(status: Request["status"]) {
    switch (status) {
      case "nouveau":
        return "default"
      case "en_analyse":
        return "secondary"
      case "accepté":
        return "success"
      case "refusé":
        return "destructive"
      case "en_cours":
        return "warning"
      case "terminé":
        return "outline"
      default:
        return "default"
    }
  }

  return (
    <div className="space-y-4">
      {requests.map((request) => (
        <div key={request.id} className="flex items-center justify-between rounded-lg border p-4">
          <div className="space-y-1">
            <p className="text-sm font-medium">
              {request.client?.type === "particulier"
                ? `${request.client?.prenom} ${request.client?.nom}`
                : request.client?.societe}
            </p>
            <p className="text-sm text-muted-foreground line-clamp-1">
              {request.description.substring(0, 100)}
              {request.description.length > 100 ? "..." : ""}
            </p>
          </div>
          <div className="flex items-center gap-4">
            <div className="text-right">
              <p className="text-sm font-medium">Demande #{request.id}</p>
              <p className="text-sm text-muted-foreground">{formatDate(request.created_at)}</p>
            </div>
            <Badge variant={getStatusBadgeVariant(request.status)}>{request.status}</Badge>
            <Button variant="outline" size="icon" asChild>
              <Link href={`/demandes/${request.id}`}>
                <Eye className="h-4 w-4" />
                <span className="sr-only">Voir la demande</span>
              </Link>
            </Button>
          </div>
        </div>
      ))}
    </div>
  )
}
