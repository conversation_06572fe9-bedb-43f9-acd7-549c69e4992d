"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import { useToast } from "@/hooks/use-toast"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { AlertCircle, ArrowLeft, Download, Eye, FileText, ImageIcon, Trash2, Brain, Zap } from "lucide-react"
import {
  getRequest,
  updateRequest,
  deletePlan,
  deletePhoto,
  type Request,
  type Plan,
  type Photo,
} from "@/services/requests"
import { formatDate } from "@/lib/utils"
import { supabase } from "@/lib/supabase"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { getAgentAnalysis, type Agent } from "@/services/agent"

interface RequestDetailPageProps {
  params: {
    id: string
  }
}

export default function RequestDetailPage({ params }: RequestDetailPageProps) {
  const router = useRouter()
  const { toast } = useToast()
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [request, setRequest] = useState<(Request & { plans?: Plan[]; photos?: Photo[] }) | null>(null)
  const [status, setStatus] = useState<Request["status"]>("nouveau")
  const [selectedFile, setSelectedFile] = useState<{ url: string; type: string } | null>(null)
  const [aiAnalysis, setAiAnalysis] = useState<Agent | null>(null)
  const [isAnalyzing, setIsAnalyzing] = useState(false)
  const [isConvertingToDevis, setIsConvertingToDevis] = useState(false)

  useEffect(() => {
    // Check if this is the "nouvelle" route and redirect
    if (params.id === "nouvelle") {
      router.replace("/demandes/nouvelle")
      return
    }

    async function loadRequest() {
      try {
        setIsLoading(true)
        setError(null)

        // Check if id is a valid number
        const requestId = Number.parseInt(params.id)
        if (isNaN(requestId)) {
          throw new Error("ID de demande invalide")
        }

        const data = await getRequest(requestId)
        setRequest(data)
        setStatus(data.status)

        // Load AI analysis if it exists
        async function loadAiAnalysis() {
          try {
            const analysis = await getAgentAnalysis(requestId)
            setAiAnalysis(analysis)
          } catch (err) {
            console.error("Erreur lors du chargement de l'analyse IA:", err)
          }
        }

        loadAiAnalysis()
      } catch (err: any) {
        console.error("Erreur lors du chargement de la demande:", err)
        setError(err.message || "Impossible de charger la demande. Veuillez réessayer plus tard.")
      } finally {
        setIsLoading(false)
      }
    }

    loadRequest()
  }, [params.id, router])

  const handleStatusChange = async (newStatus: Request["status"]) => {
    try {
      if (!request) return

      setIsLoading(true)
      await updateRequest(request.id, { status: newStatus })
      setStatus(newStatus)
      setRequest((prev) => (prev ? { ...prev, status: newStatus } : null))
      toast({
        title: "Statut mis à jour",
        description: "Le statut de la demande a été mis à jour avec succès",
      })
    } catch (err: any) {
      console.error("Erreur lors de la mise à jour du statut:", err)
      toast({
        title: "Erreur",
        description: err.message || "Une erreur est survenue lors de la mise à jour du statut",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleDeletePlan = async (planId: number) => {
    try {
      setIsLoading(true)
      await deletePlan(planId)
      setRequest((prev) =>
        prev
          ? {
              ...prev,
              plans: prev.plans?.filter((plan) => plan.id !== planId),
            }
          : null,
      )
      toast({
        title: "Plan supprimé",
        description: "Le plan a été supprimé avec succès",
      })
    } catch (err: any) {
      console.error("Erreur lors de la suppression du plan:", err)
      toast({
        title: "Erreur",
        description: err.message || "Une erreur est survenue lors de la suppression du plan",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleDeletePhoto = async (photoId: number) => {
    try {
      setIsLoading(true)
      await deletePhoto(photoId)
      setRequest((prev) =>
        prev
          ? {
              ...prev,
              photos: prev.photos?.filter((photo) => photo.id !== photoId),
            }
          : null,
      )
      toast({
        title: "Photo supprimée",
        description: "La photo a été supprimée avec succès",
      })
    } catch (err: any) {
      console.error("Erreur lors de la suppression de la photo:", err)
      toast({
        title: "Erreur",
        description: err.message || "Une erreur est survenue lors de la suppression de la photo",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const getFileUrl = async (path: string) => {
    const { data } = await supabase.storage.from("client-files").getPublicUrl(path)
    return data.publicUrl
  }

  const viewFile = async (path: string, type: string) => {
    try {
      const url = await getFileUrl(path)
      setSelectedFile({ url, type })
    } catch (err) {
      console.error("Erreur lors de l'obtention de l'URL du fichier:", err)
      toast({
        title: "Erreur",
        description: "Impossible d'afficher le fichier",
        variant: "destructive",
      })
    }
  }

  const downloadFile = async (path: string, fileName: string) => {
    try {
      const url = await getFileUrl(path)
      const a = document.createElement("a")
      a.href = url
      a.download = fileName
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
    } catch (err) {
      console.error("Erreur lors du téléchargement du fichier:", err)
      toast({
        title: "Erreur",
        description: "Impossible de télécharger le fichier",
        variant: "destructive",
      })
    }
  }

  function getStatusBadgeVariant(status: Request["status"]) {
    switch (status) {
      case "nouveau":
        return "default"
      case "en_analyse":
        return "secondary"
      case "accepté":
        return "success"
      case "refusé":
        return "destructive"
      case "en_cours":
        return "warning"
      case "terminé":
        return "outline"
      default:
        return "default"
    }
  }

  const handleAnalyzeWithAI = async () => {
    try {
      setIsAnalyzing(true)
      const response = await fetch("/api/agent/analyze", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ requestId: request?.id }),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || "Erreur lors de l'analyse")
      }

      const analysis = await response.json()
      setAiAnalysis(analysis)
      toast({
        title: "Analyse terminée",
        description: "L'analyse IA a été effectuée avec succès",
      })
    } catch (err: any) {
      console.error("Erreur lors de l'analyse IA:", err)
      toast({
        title: "Erreur",
        description: err.message || "Une erreur est survenue lors de l'analyse IA",
        variant: "destructive",
      })
    } finally {
      setIsAnalyzing(false)
    }
  }

  const handleConvertToDevis = async () => {
    try {
      setIsConvertingToDevis(true)
      const response = await fetch("/api/agent/convert-to-devis", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          requestId: request?.id,
          userId: 1, // TODO: Get actual user ID from auth context
        }),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || "Erreur lors de la conversion")
      }

      const result = await response.json()
      toast({
        title: "Devis créé",
        description: "Le devis a été créé avec succès à partir de l'analyse IA",
      })

      // Redirect to devis page
      router.push(`/devis/${result.id}`)
    } catch (err: any) {
      console.error("Erreur lors de la conversion en devis:", err)
      toast({
        title: "Erreur",
        description: err.message || "Une erreur est survenue lors de la conversion en devis",
        variant: "destructive",
      })
    } finally {
      setIsConvertingToDevis(false)
    }
  }

  // Early return for "nouvelle" route (should not reach here due to redirect)
  if (params.id === "nouvelle") {
    return null
  }

  if (isLoading) {
    return (
      <div className="flex flex-col gap-4">
        <div className="flex items-center gap-2">
          <Button variant="outline" size="icon" disabled>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <h1 className="text-2xl font-bold">Chargement...</h1>
        </div>
        <Card>
          <CardHeader>
            <CardTitle>Détails de la demande</CardTitle>
            <CardDescription>Chargement des informations...</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex justify-center p-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (error || !request) {
    return (
      <div className="flex flex-col gap-4">
        <div className="flex items-center gap-2">
          <Button variant="outline" size="icon" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <h1 className="text-2xl font-bold">Erreur</h1>
        </div>
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error || "Impossible de charger la demande"}</AlertDescription>
        </Alert>
        <Button onClick={() => router.push("/demandes")}>Retour à la liste des demandes</Button>
      </div>
    )
  }

  return (
    <div className="flex flex-col gap-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Button variant="outline" size="icon" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <h1 className="text-2xl font-bold">Demande #{request.id}</h1>
          <Badge variant={getStatusBadgeVariant(request.status)}>{request.status}</Badge>
        </div>
        <div className="flex items-center gap-2">
          <Select value={status} onValueChange={(value) => handleStatusChange(value as Request["status"])}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Changer le statut" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="nouveau">Nouveau</SelectItem>
              <SelectItem value="en_analyse">En analyse</SelectItem>
              <SelectItem value="accepté">Accepté</SelectItem>
              <SelectItem value="refusé">Refusé</SelectItem>
              <SelectItem value="en_cours">En cours</SelectItem>
              <SelectItem value="terminé">Terminé</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Informations du client</CardTitle>
          <CardDescription>Détails du client ayant fait la demande</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h3 className="text-sm font-medium text-muted-foreground">Client</h3>
              <p className="text-base">
                {request.client?.type === "particulier"
                  ? `${request.client?.prenom} ${request.client?.nom}`
                  : request.client?.societe}
              </p>
            </div>
            <div>
              <h3 className="text-sm font-medium text-muted-foreground">Type</h3>
              <p className="text-base capitalize">{request.client?.type}</p>
            </div>
            <div>
              <h3 className="text-sm font-medium text-muted-foreground">Date de la demande</h3>
              <p className="text-base">{formatDate(request.created_at)}</p>
            </div>
            <div>
              <h3 className="text-sm font-medium text-muted-foreground">Dernière mise à jour</h3>
              <p className="text-base">{formatDate(request.updated_at)}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Description du problème</CardTitle>
          <CardDescription>Description fournie par le client</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="whitespace-pre-wrap rounded-md bg-muted p-4">{request.description}</div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Brain className="h-5 w-5" />
            Analyse par Intelligence Artificielle
          </CardTitle>
          <CardDescription>
            Analysez cette demande avec l'IA pour obtenir des recommandations détaillées
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center gap-2">
            <Button
              onClick={handleAnalyzeWithAI}
              disabled={isAnalyzing || isLoading}
              className="flex items-center gap-2"
            >
              {isAnalyzing ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  Analyse en cours...
                </>
              ) : (
                <>
                  <Zap className="h-4 w-4" />
                  Analyser avec l'IA
                </>
              )}
            </Button>

            {aiAnalysis && (
              <Button
                onClick={handleConvertToDevis}
                disabled={isConvertingToDevis || isLoading}
                variant="outline"
                className="flex items-center gap-2"
              >
                {isConvertingToDevis ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-900"></div>
                    Conversion en cours...
                  </>
                ) : (
                  <>
                    <FileText className="h-4 w-4" />
                    Convertir en devis
                  </>
                )}
              </Button>
            )}
          </div>

          {aiAnalysis && (
            <div className="mt-4">
              <h4 className="text-sm font-medium mb-2">Résultat de l'analyse :</h4>
              <div className="whitespace-pre-wrap rounded-md bg-muted p-4 text-sm max-h-96 overflow-y-auto">
                {aiAnalysis.agent_response}
              </div>
              <div className="text-xs text-muted-foreground mt-2">Analysé le {formatDate(aiAnalysis.created_at)}</div>
            </div>
          )}
        </CardContent>
      </Card>

      <Tabs defaultValue="plans" className="space-y-4">
        <TabsList>
          <TabsTrigger value="plans">Plans ({request.plans?.length || 0})</TabsTrigger>
          <TabsTrigger value="photos">Photos ({request.photos?.length || 0})</TabsTrigger>
        </TabsList>
        <TabsContent value="plans">
          <Card>
            <CardHeader>
              <CardTitle>Plans</CardTitle>
              <CardDescription>Plans fournis par le client</CardDescription>
            </CardHeader>
            <CardContent>
              {request.plans && request.plans.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {request.plans.map((plan) => (
                    <div key={plan.id} className="flex flex-col rounded-md border p-4">
                      <div className="flex items-center gap-2 mb-2">
                        <FileText className="h-5 w-5 text-muted-foreground" />
                        <span className="text-sm font-medium truncate">{plan.file_name}</span>
                      </div>
                      <div className="text-xs text-muted-foreground mb-4">{formatDate(plan.uploaded_at)}</div>
                      <div className="mt-auto flex items-center justify-between">
                        <div className="flex gap-2">
                          <Button variant="outline" size="sm" onClick={() => viewFile(plan.file_path, plan.file_type)}>
                            <Eye className="h-4 w-4 mr-1" />
                            Voir
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => downloadFile(plan.file_path, plan.file_name)}
                          >
                            <Download className="h-4 w-4 mr-1" />
                            Télécharger
                          </Button>
                        </div>
                        <Button variant="ghost" size="icon" onClick={() => handleDeletePlan(plan.id)}>
                          <Trash2 className="h-4 w-4 text-destructive" />
                          <span className="sr-only">Supprimer</span>
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center p-4 text-muted-foreground">Aucun plan disponible</div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="photos">
          <Card>
            <CardHeader>
              <CardTitle>Photos</CardTitle>
              <CardDescription>Photos fournies par le client</CardDescription>
            </CardHeader>
            <CardContent>
              {request.photos && request.photos.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {request.photos.map((photo) => (
                    <div key={photo.id} className="flex flex-col rounded-md border p-4">
                      <div className="flex items-center gap-2 mb-2">
                        <ImageIcon className="h-5 w-5 text-muted-foreground" />
                        <span className="text-sm font-medium truncate">{photo.file_name}</span>
                      </div>
                      <div className="text-xs text-muted-foreground mb-4">{formatDate(photo.uploaded_at)}</div>
                      <div className="mt-auto flex items-center justify-between">
                        <div className="flex gap-2">
                          <Button variant="outline" size="sm" onClick={() => viewFile(photo.file_path, "image")}>
                            <Eye className="h-4 w-4 mr-1" />
                            Voir
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => downloadFile(photo.file_path, photo.file_name)}
                          >
                            <Download className="h-4 w-4 mr-1" />
                            Télécharger
                          </Button>
                        </div>
                        <Button variant="ghost" size="icon" onClick={() => handleDeletePhoto(photo.id)}>
                          <Trash2 className="h-4 w-4 text-destructive" />
                          <span className="sr-only">Supprimer</span>
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center p-4 text-muted-foreground">Aucune photo disponible</div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* File viewer dialog */}
      <Dialog open={!!selectedFile} onOpenChange={(open) => !open && setSelectedFile(null)}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>Aperçu du fichier</DialogTitle>
            <DialogDescription>{selectedFile?.type.includes("pdf") ? "Document PDF" : "Image"}</DialogDescription>
          </DialogHeader>
          <div className="flex justify-center p-4 max-h-[70vh] overflow-auto">
            {selectedFile?.type.includes("pdf") ? (
              <iframe src={`${selectedFile.url}#toolbar=0`} className="w-full h-[60vh]" title="PDF Viewer" />
            ) : (
              <img
                src={selectedFile?.url || "/placeholder.svg"}
                alt="Preview"
                className="max-w-full max-h-[60vh] object-contain"
              />
            )}
          </div>
          <div className="flex justify-end">
            <Button variant="outline" onClick={() => setSelectedFile(null)}>
              Fermer
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}
