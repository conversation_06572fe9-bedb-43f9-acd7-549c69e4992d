"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useToast } from "@/hooks/use-toast"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import {
  ArrowLeft,
  FileText,
  ImageIcon,
  Trash2,
  Upload,
  AlertCircle,
  CheckCircle2,
  RefreshCw,
  ExternalLink,
} from "lucide-react"
import { createRequest, uploadPlan, uploadPhoto, testBucketAccess } from "@/services/requests"
import { getClients } from "@/services/clients"

interface Client {
  id: number
  nom: string
  prenom: string
  societe: string
  type: string
}

interface FileWithPreview extends File {
  preview?: string
  description?: string
}

const MAX_FILE_SIZE = 5 * 1024 * 1024 // 5MB

export default function NewRequestPage() {
  const router = useRouter()
  const { toast } = useToast()
  const [isLoading, setIsLoading] = useState(false)
  const [clients, setClients] = useState<Client[]>([])
  const [clientId, setClientId] = useState<string>("")
  const [description, setDescription] = useState("")
  const [plans, setPlans] = useState<FileWithPreview[]>([])
  const [photos, setPhotos] = useState<FileWithPreview[]>([])
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [bucketStatus, setBucketStatus] = useState<{
    success: boolean
    message: string
    error?: any
    availableBuckets?: string[]
    bucketName?: string
  } | null>(null)
  const [isCheckingBucket, setIsCheckingBucket] = useState(true)
  const [bucketName, setBucketName] = useState<string>(
    process.env.NEXT_PUBLIC_SUPABASE_CLIENT_FILES_BUCKET || "client-files",
  )

  useEffect(() => {
    async function loadClients() {
      try {
        const data = await getClients()
        setClients(data)
      } catch (err) {
        console.error("Erreur lors du chargement des clients:", err)
        toast({
          title: "Erreur",
          description: "Impossible de charger la liste des clients",
          variant: "destructive",
        })
      }
    }

    async function checkBucket() {
      setIsCheckingBucket(true)
      try {
        const result = await testBucketAccess()
        setBucketStatus(result)

        if (!result.success) {
          setError(result.message || "Le bucket de stockage n'est pas correctement configuré.")
        }
      } catch (err: any) {
        console.error("Erreur lors de la vérification du bucket:", err)
        setError(`Erreur de stockage: ${err.message || "Impossible de vérifier le bucket de stockage"}`)
      } finally {
        setIsCheckingBucket(false)
      }
    }

    loadClients()
    checkBucket()

    // Cleanup previews on unmount
    return () => {
      plans.forEach((plan) => plan.preview && URL.revokeObjectURL(plan.preview))
      photos.forEach((photo) => photo.preview && URL.revokeObjectURL(photo.preview))
    }
  }, [toast])

  const handleTestBucket = async () => {
    setIsCheckingBucket(true)
    setError(null)

    try {
      const result = await testBucketAccess()
      setBucketStatus(result)

      if (result.success) {
        toast({
          title: "Bucket fonctionnel",
          description: result.message,
        })
      } else {
        setError(result.message || "Le bucket de stockage n'est pas correctement configuré.")
        toast({
          title: "Erreur",
          description: result.message || "Le bucket de stockage n'est pas correctement configuré.",
          variant: "destructive",
        })
      }
    } catch (err: any) {
      console.error("Erreur lors du test du bucket:", err)
      setError(`Erreur de stockage: ${err.message || "Impossible de tester le bucket de stockage"}`)
      toast({
        title: "Erreur",
        description: `Erreur de stockage: ${err.message || "Impossible de tester le bucket de stockage"}`,
        variant: "destructive",
      })
    } finally {
      setIsCheckingBucket(false)
    }
  }

  const handlePlanUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!e.target.files || e.target.files.length === 0) return

    const newPlans: FileWithPreview[] = Array.from(e.target.files)
      .map((file) => {
        if (file.size > MAX_FILE_SIZE) {
          toast({
            title: "Erreur",
            description: `Le fichier ${file.name} dépasse la taille maximale autorisée (5MB)`,
            variant: "destructive",
          })
          return null
        }
        const fileWithPreview = file as FileWithPreview
        fileWithPreview.preview = URL.createObjectURL(file)
        return fileWithPreview
      })
      .filter(Boolean) as FileWithPreview[]

    setPlans((prev) => [...prev, ...newPlans])
    e.target.value = "" // Reset input
  }

  const handlePhotoUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!e.target.files || e.target.files.length === 0) return

    const newPhotos: FileWithPreview[] = Array.from(e.target.files)
      .map((file) => {
        if (file.size > MAX_FILE_SIZE) {
          toast({
            title: "Erreur",
            description: `Le fichier ${file.name} dépasse la taille maximale autorisée (5MB)`,
            variant: "destructive",
          })
          return null
        }
        const fileWithPreview = file as FileWithPreview
        fileWithPreview.preview = URL.createObjectURL(file)
        return fileWithPreview
      })
      .filter(Boolean) as FileWithPreview[]

    setPhotos((prev) => [...prev, ...newPhotos])
    e.target.value = "" // Reset input
  }

  const handleRemovePlan = (index: number) => {
    const planToRemove = plans[index]
    if (planToRemove.preview) {
      URL.revokeObjectURL(planToRemove.preview)
    }
    setPlans((prev) => prev.filter((_, i) => i !== index))
  }

  const handleRemovePhoto = (index: number) => {
    const photoToRemove = photos[index]
    if (photoToRemove.preview) {
      URL.revokeObjectURL(photoToRemove.preview)
    }
    setPhotos((prev) => prev.filter((_, i) => i !== index))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError(null)

    if (!clientId) {
      toast({
        title: "Erreur",
        description: "Veuillez sélectionner un client",
        variant: "destructive",
      })
      return
    }

    if (!description.trim()) {
      toast({
        title: "Erreur",
        description: "Veuillez fournir une description du problème",
        variant: "destructive",
      })
      return
    }

    if (plans.length === 0) {
      toast({
        title: "Erreur",
        description: "Veuillez ajouter au moins un plan",
        variant: "destructive",
      })
      return
    }

    try {
      setIsSubmitting(true)

      // Create the request
      const request = await createRequest({
        client_id: Number.parseInt(clientId),
        description: description.trim(),
        status: "nouveau",
      })

      // Upload plans
      for (const plan of plans) {
        await uploadPlan(request.id, plan, plan.description)
      }

      // Upload photos
      for (const photo of photos) {
        await uploadPhoto(request.id, photo, photo.description)
      }

      toast({
        title: "Demande créée",
        description: "La demande a été créée avec succès",
      })

      // Redirect to the request list
      router.push("/demandes")
    } catch (err: any) {
      console.error("Erreur lors de la création de la demande:", err)
      setError(err.message || "Une erreur est survenue lors de la création de la demande")
      toast({
        title: "Erreur",
        description: err.message || "Une erreur est survenue lors de la création de la demande",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  const getClientDisplayName = (client: Client) => {
    return client.type === "particulier" ? `${client.prenom} ${client.nom}` : client.societe
  }

  return (
    <div className="flex flex-col gap-4">
      <div className="flex items-center gap-2">
        <Button variant="outline" size="icon" onClick={() => router.back()}>
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <h1 className="text-2xl font-bold">Nouvelle demande</h1>
      </div>

      {isCheckingBucket ? (
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Vérification du stockage</AlertTitle>
          <AlertDescription>
            Vérification de la configuration du bucket de stockage &apos;{bucketName}&apos;...
          </AlertDescription>
        </Alert>
      ) : bucketStatus?.success ? (
        <Alert>
          <CheckCircle2 className="h-4 w-4 text-green-500" />
          <AlertTitle>Stockage configuré</AlertTitle>
          <AlertDescription>
            <div className="flex justify-between items-center">
              <span>{bucketStatus.message}</span>
              <Button variant="outline" size="sm" onClick={handleTestBucket}>
                <RefreshCw className="h-4 w-4 mr-2" />
                Tester à nouveau
              </Button>
            </div>
          </AlertDescription>
        </Alert>
      ) : (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Configuration requise</AlertTitle>
          <AlertDescription>
            <p>
              {error ||
                `Le bucket de stockage '${bucketStatus?.bucketName || bucketName}' n'existe pas ou n'est pas correctement configuré.`}
            </p>

            {bucketStatus?.availableBuckets && bucketStatus.availableBuckets.length > 0 && (
              <div className="mt-2">
                <p>Buckets disponibles:</p>
                <ul className="list-disc pl-5">
                  {bucketStatus.availableBuckets.map((bucket) => (
                    <li key={bucket}>{bucket}</li>
                  ))}
                </ul>
              </div>
            )}

            <div className="mt-4">
              <p className="font-medium">Pour créer le bucket dans Supabase:</p>
              <ol className="list-decimal pl-5 mt-2 space-y-1">
                <li>
                  Connectez-vous à votre{" "}
                  <a
                    href="https://app.supabase.com"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-400 hover:underline"
                  >
                    dashboard Supabase
                  </a>
                </li>
                <li>Sélectionnez votre projet</li>
                <li>Cliquez sur &quot;Storage&quot; dans le menu de gauche</li>
                <li>Cliquez sur &quot;New Bucket&quot;</li>
                <li>Nommez le bucket exactement &quot;{bucketStatus?.bucketName || bucketName}&quot;</li>
                <li>Sélectionnez &quot;Public&quot; pour le type de bucket</li>
                <li>Cliquez sur &quot;Create bucket&quot;</li>
                <li>Ajoutez les politiques d&apos;accès nécessaires</li>
              </ol>
            </div>

            <div className="flex gap-2 mt-4">
              <Button variant="outline" size="sm" onClick={handleTestBucket} disabled={isCheckingBucket}>
                <RefreshCw className="h-4 w-4 mr-2" />
                Tester à nouveau
              </Button>

              <Button variant="outline" size="sm" onClick={() => window.open("https://app.supabase.com", "_blank")}>
                <ExternalLink className="h-4 w-4 mr-2" />
                Ouvrir Supabase
              </Button>
            </div>
          </AlertDescription>
        </Alert>
      )}

      <form onSubmit={handleSubmit} className="space-y-4">
        <Card>
          <CardHeader>
            <CardTitle>Informations générales</CardTitle>
            <CardDescription>Sélectionnez un client et décrivez le problème</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="client">Client</Label>
              <Select value={clientId} onValueChange={setClientId}>
                <SelectTrigger>
                  <SelectValue placeholder="Sélectionnez un client" />
                </SelectTrigger>
                <SelectContent>
                  {clients.map((client) => (
                    <SelectItem key={client.id} value={client.id.toString()}>
                      {getClientDisplayName(client)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description du problème</Label>
              <Textarea
                id="description"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                placeholder="Décrivez le problème en détail..."
                rows={5}
                required
              />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Plans</CardTitle>
            <CardDescription>Ajoutez un ou plusieurs plans (PDF, images)</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center gap-4">
              <Label
                htmlFor="plan-upload"
                className={`flex h-10 cursor-pointer items-center gap-2 rounded-md px-4 text-primary-foreground ${
                  bucketStatus?.success ? "bg-primary hover:bg-primary/90" : "bg-primary/50 cursor-not-allowed"
                }`}
              >
                <Upload className="h-4 w-4" />
                Ajouter des plans
              </Label>
              <Input
                id="plan-upload"
                type="file"
                accept=".pdf,.jpg,.jpeg,.png"
                multiple
                className="hidden"
                onChange={handlePlanUpload}
                disabled={!bucketStatus?.success}
              />
            </div>

            {plans.length > 0 && (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mt-4">
                {plans.map((plan, index) => (
                  <div key={index} className="flex flex-col rounded-md border p-4">
                    <div className="flex items-center gap-2 mb-2">
                      <FileText className="h-5 w-5 text-muted-foreground" />
                      <span className="text-sm font-medium truncate">{plan.name}</span>
                    </div>
                    <div className="text-xs text-muted-foreground mb-4">{(plan.size / 1024).toFixed(2)} KB</div>
                    <div className="mt-auto flex justify-end">
                      <Button variant="ghost" size="icon" onClick={() => handleRemovePlan(index)}>
                        <Trash2 className="h-4 w-4 text-destructive" />
                        <span className="sr-only">Supprimer</span>
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Photos</CardTitle>
            <CardDescription>Ajoutez une ou plusieurs photos</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center gap-4">
              <Label
                htmlFor="photo-upload"
                className={`flex h-10 cursor-pointer items-center gap-2 rounded-md px-4 text-primary-foreground ${
                  bucketStatus?.success ? "bg-primary hover:bg-primary/90" : "bg-primary/50 cursor-not-allowed"
                }`}
              >
                <Upload className="h-4 w-4" />
                Ajouter des photos
              </Label>
              <Input
                id="photo-upload"
                type="file"
                accept=".jpg,.jpeg,.png"
                multiple
                className="hidden"
                onChange={handlePhotoUpload}
                disabled={!bucketStatus?.success}
              />
            </div>

            {photos.length > 0 && (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mt-4">
                {photos.map((photo, index) => (
                  <div key={index} className="flex flex-col rounded-md border p-4">
                    <div className="flex items-center gap-2 mb-2">
                      <ImageIcon className="h-5 w-5 text-muted-foreground" />
                      <span className="text-sm font-medium truncate">{photo.name}</span>
                    </div>
                    {photo.preview && (
                      <div className="mb-2 rounded-md overflow-hidden">
                        <img
                          src={photo.preview || "/placeholder.svg"}
                          alt={photo.name}
                          className="w-full h-32 object-cover"
                        />
                      </div>
                    )}
                    <div className="text-xs text-muted-foreground mb-4">{(photo.size / 1024).toFixed(2)} KB</div>
                    <div className="mt-auto flex justify-end">
                      <Button variant="ghost" size="icon" onClick={() => handleRemovePhoto(index)}>
                        <Trash2 className="h-4 w-4 text-destructive" />
                        <span className="sr-only">Supprimer</span>
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        <div className="flex justify-end gap-4">
          <Button variant="outline" onClick={() => router.back()} disabled={isSubmitting}>
            Annuler
          </Button>
          <Button type="submit" disabled={isSubmitting || !bucketStatus?.success}>
            {isSubmitting ? "Création en cours..." : "Créer la demande"}
          </Button>
        </div>
      </form>
    </div>
  )
}
