"use client"

import { useEffect } from "react"
import { useRouter } from "next/navigation"
import { useSupabase } from "@/hooks/use-supabase"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"

export default function LogoutPage() {
  const router = useRouter()
  const supabase = useSupabase()

  useEffect(() => {
    const handleLogout = async () => {
      await supabase.auth.signOut()
      router.push("/login")
    }

    handleLogout()
  }, [router, supabase])

  return (
    <div className="flex h-screen w-full items-center justify-center">
      <Card className="w-full max-w-md">
        <CardHeader className="space-y-1">
          <CardTitle className="text-2xl font-bold">Déconnexion en cours...</CardTitle>
          <CardDescription>Vous êtes en train d'être déconnecté de votre compte.</CardDescription>
        </CardHeader>
        <CardContent className="flex justify-center">
          <Button onClick={() => router.push("/login")}>Retour à la page de connexion</Button>
        </CardContent>
      </Card>
    </div>
  )
}
