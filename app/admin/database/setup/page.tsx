"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { AlertCircle, CheckCircle } from "lucide-react"
import Link from "next/link"

export default function SetupRpcPage() {
  const [isLoading, setIsLoading] = useState(false)
  const [result, setResult] = useState<{ success: boolean; message: string } | null>(null)

  const setupRpc = async () => {
    setIsLoading(true)
    try {
      const response = await fetch("/api/setup-rpc/create-function")
      const data = await response.json()
      setResult(data)
    } catch (error) {
      setResult({
        success: false,
        message: "Une erreur s'est produite lors de la configuration.",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="container mx-auto py-10">
      <h1 className="mb-8 text-3xl font-bold">Configuration de la Fonction SQL</h1>

      <Card className="w-full">
        <CardHeader>
          <CardTitle>Étape préliminaire</CardTitle>
          <CardDescription>
            Créez la fonction SQL nécessaire pour exécuter les scripts de création de tables
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <p className="text-sm text-muted-foreground">
              Avant de pouvoir créer les tables de la base de données, nous devons configurer une fonction SQL spéciale
              dans votre base de données Supabase. Cette fonction permettra d'exécuter les scripts SQL de création de
              tables.
            </p>

            <div className="rounded-md bg-amber-50 p-4">
              <div className="flex">
                <div className="flex-shrink-0">
                  <AlertCircle className="h-5 w-5 text-amber-400" />
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-amber-800">Important</h3>
                  <div className="mt-2 text-sm text-amber-700">
                    <p>
                      Si cette étape échoue, vous devrez créer manuellement la fonction SQL dans l'interface Supabase :
                    </p>
                    <ol className="list-decimal pl-5 pt-2">
                      <li>Allez dans l'interface SQL de Supabase</li>
                      <li>Exécutez le script SQL suivant :</li>
                    </ol>
                    <pre className="mt-2 rounded bg-gray-800 p-2 text-xs text-white">
                      {`CREATE OR REPLACE FUNCTION exec_sql(sql_query text)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  EXECUTE sql_query;
END;
$$;`}
                    </pre>
                  </div>
                </div>
              </div>
            </div>

            {result && (
              <Alert variant={result.success ? "default" : "destructive"}>
                {result.success ? <CheckCircle className="h-4 w-4" /> : <AlertCircle className="h-4 w-4" />}
                <AlertTitle>{result.success ? "Succès" : "Erreur"}</AlertTitle>
                <AlertDescription>{result.message}</AlertDescription>
              </Alert>
            )}
          </div>
        </CardContent>
        <CardFooter className="flex justify-between">
          <Button onClick={setupRpc} disabled={isLoading}>
            {isLoading ? "Configuration en cours..." : "Configurer la fonction SQL"}
          </Button>

          {result?.success && (
            <Button asChild variant="outline">
              <Link href="/admin/database">Continuer vers la création des tables</Link>
            </Button>
          )}
        </CardFooter>
      </Card>
    </div>
  )
}
