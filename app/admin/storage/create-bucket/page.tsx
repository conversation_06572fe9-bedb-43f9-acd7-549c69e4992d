"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { CheckCircle2, Loader2 } from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { useRouter } from "next/navigation"
import { supabase } from "@/lib/supabase"

export default function CreateBucketPage() {
  const { toast } = useToast()
  const router = useRouter()
  const [isCreating, setIsCreating] = useState(false)
  const [isCreated, setIsCreated] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const createBucket = async () => {
    setIsCreating(true)
    setError(null)

    try {
      // Check if Supabase client is available
      if (!supabase) {
        setError("Client Supabase non disponible. Vérifiez votre connexion et les variables d'environnement.")
        return
      }

      // First check if the bucket already exists
      const { data: buckets, error: listError } = await supabase.storage.listBuckets()

      if (listError) {
        setError(`Erreur lors de la vérification des buckets: ${listError.message}`)
        return
      }

      const bucketExists = buckets.some((bucket) => bucket.name === "client-files")

      if (bucketExists) {
        toast({
          title: "Bucket déjà existant",
          description: "Le bucket 'client-files' existe déjà.",
        })
        setIsCreated(true)
        return
      }

      // Create the bucket
      const { data, error: createError } = await supabase.storage.createBucket("client-files", {
        public: true,
      })

      if (createError) {
        if (createError.message.includes("already exists")) {
          toast({
            title: "Bucket déjà existant",
            description: "Le bucket 'client-files' existe déjà.",
          })
          setIsCreated(true)
          return
        }

        setError(`Erreur lors de la création du bucket: ${createError.message}`)
        return
      }

      setIsCreated(true)
      toast({
        title: "Bucket créé",
        description: "Le bucket 'client-files' a été créé avec succès.",
      })
    } catch (err) {
      console.error("Error creating bucket:", err)
      setError(`Erreur lors de la création du bucket: ${err.message || "Erreur inconnue"}`)
    } finally {
      setIsCreating(false)
    }
  }

  return (
    <div className="container mx-auto py-6">
      <h1 className="text-2xl font-bold mb-6">Création du bucket de stockage</h1>

      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Créer le bucket &apos;client-files&apos;</CardTitle>
          <CardDescription>
            Ce bucket est nécessaire pour stocker les fichiers des clients (plans et photos).
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {error && (
            <Alert variant="destructive">
              <AlertTitle>Erreur</AlertTitle>
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {isCreated && (
            <Alert>
              <CheckCircle2 className="h-4 w-4 text-green-500" />
              <AlertTitle>Bucket créé</AlertTitle>
              <AlertDescription>Le bucket &apos;client-files&apos; a été créé avec succès.</AlertDescription>
            </Alert>
          )}

          <div className="border rounded-md p-4">
            <h3 className="text-lg font-medium">Informations sur le bucket</h3>
            <ul className="list-disc pl-5 mt-2 space-y-1">
              <li>Nom: client-files</li>
              <li>Type: Public</li>
              <li>Utilisation: Stockage des plans et photos des clients</li>
            </ul>
          </div>
        </CardContent>
        <CardFooter className="flex justify-between">
          <Button variant="outline" onClick={() => router.push("/admin/storage/verification")}>
            Vérifier le bucket
          </Button>
          <Button onClick={createBucket} disabled={isCreating || isCreated}>
            {isCreating ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Création en cours...
              </>
            ) : isCreated ? (
              "Bucket créé"
            ) : (
              "Créer le bucket"
            )}
          </Button>
        </CardFooter>
      </Card>

      {isCreated && (
        <Card>
          <CardHeader>
            <CardTitle>Configurer les politiques d&apos;accès</CardTitle>
            <CardDescription>
              Pour que le bucket fonctionne correctement, vous devez configurer les politiques d&apos;accès.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <p>
              Exécutez le SQL suivant dans l&apos;éditeur SQL de Supabase pour configurer les politiques d&apos;accès:
            </p>
            <pre className="p-4 bg-slate-800 text-white rounded text-sm overflow-x-auto">
              {`-- Pour la lecture (SELECT)
CREATE POLICY "Allow users to select their own files"
ON storage.objects FOR SELECT TO authenticated USING (
  bucket_id = 'client-files'
);

-- Pour l'upload (INSERT)
CREATE POLICY "Allow authenticated users to upload files"
ON storage.objects FOR INSERT TO authenticated USING (
  bucket_id = 'client-files' AND auth.role() = 'authenticated'
);

-- Pour la suppression (DELETE)
CREATE POLICY "Allow users to delete their own files"
ON storage.objects FOR DELETE TO authenticated USING (
  bucket_id = 'client-files'
);`}
            </pre>
          </CardContent>
          <CardFooter>
            <Button onClick={() => router.push("/demandes/nouvelle")}>Créer une nouvelle demande</Button>
          </CardFooter>
        </Card>
      )}
    </div>
  )
}
