"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { CheckCircle2, XCircle, AlertCircle, Loader2 } from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { useRouter } from "next/navigation"

// Define the result type
interface BucketCheckResult {
  success: boolean
  exists?: boolean
  canCheckPolicies?: boolean
  policies?: any[]
  message?: string
  error?: string
  details?: any
}

export default function BucketVerificationPage() {
  const { toast } = useToast()
  const router = useRouter()
  const [isChecking, setIsChecking] = useState(false)
  const [result, setResult] = useState<BucketCheckResult | null>(null)
  const [error, setError] = useState<string | null>(null)

  const checkBucket = async () => {
    setIsChecking(true)
    setError(null)

    try {
      const response = await fetch("/api/storage/check-bucket")
      const data = await response.json()

      setResult(data)

      if (data.success) {
        toast({
          title: "Vérification terminée",
          description: data.message || "Le bucket existe",
        })
      } else {
        toast({
          title: "Problème détecté",
          description: data.error || "Un problème a été détecté avec le bucket",
          variant: "destructive",
        })
      }
    } catch (err) {
      console.error("Error checking bucket:", err)
      setError("Erreur lors de la vérification du bucket: " + (err.message || "Erreur inconnue"))
      toast({
        title: "Erreur",
        description: "Une erreur est survenue lors de la vérification du bucket",
        variant: "destructive",
      })
    } finally {
      setIsChecking(false)
    }
  }

  // Check on page load
  useEffect(() => {
    checkBucket()
  }, [])

  return (
    <div className="container mx-auto py-6">
      <h1 className="text-2xl font-bold mb-6">Vérification du bucket de stockage</h1>

      {isChecking && !result && (
        <Card>
          <CardContent className="flex items-center justify-center p-6">
            <Loader2 className="h-8 w-8 animate-spin text-primary mr-2" />
            <span>Vérification du bucket en cours...</span>
          </CardContent>
        </Card>
      )}

      {error && (
        <Alert variant="destructive" className="mb-6">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Erreur</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {result && (
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>État du bucket &apos;client-files&apos;</CardTitle>
            <CardDescription>Vérification de l&apos;existence et de la configuration du bucket.</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Alert variant={result.success ? "default" : "destructive"}>
              {result.success ? (
                <CheckCircle2 className="h-4 w-4 text-green-500" />
              ) : (
                <AlertCircle className="h-4 w-4" />
              )}
              <AlertTitle>{result.success ? "Bucket trouvé" : "Problème détecté"}</AlertTitle>
              <AlertDescription>{result.message || result.error || "Vérification terminée"}</AlertDescription>
            </Alert>

            {result.exists !== undefined && (
              <div className="border rounded-md p-4">
                <div className="flex items-center">
                  {result.exists ? (
                    <CheckCircle2 className="h-5 w-5 text-green-500 mr-2" />
                  ) : (
                    <XCircle className="h-5 w-5 text-red-500 mr-2" />
                  )}
                  <span className="font-medium">Existence du bucket</span>
                </div>
                <p className="text-sm text-muted-foreground mt-1">
                  {result.exists ? "Le bucket 'client-files' existe." : "Le bucket 'client-files' n'existe pas."}
                </p>
              </div>
            )}

            {result.canCheckPolicies && result.policies && (
              <div>
                <h3 className="text-lg font-medium mb-2">Politiques du bucket</h3>
                <div className="border rounded-md p-4 overflow-auto max-h-60">
                  <pre className="text-xs">{JSON.stringify(result.policies, null, 2)}</pre>
                </div>
              </div>
            )}
          </CardContent>
          <CardFooter className="flex justify-between">
            <Button variant="outline" onClick={() => router.push("/dashboard")}>
              Retour au tableau de bord
            </Button>
            <Button onClick={checkBucket} disabled={isChecking}>
              {isChecking ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Vérification...
                </>
              ) : (
                "Vérifier à nouveau"
              )}
            </Button>
          </CardFooter>
        </Card>
      )}

      {result && !result.exists && (
        <Card>
          <CardHeader>
            <CardTitle>Création du bucket</CardTitle>
            <CardDescription>
              Le bucket &apos;client-files&apos; n&apos;existe pas. Voici comment le créer.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <h3 className="text-lg font-medium">Créer le bucket via l&apos;interface Supabase</h3>
            <ol className="list-decimal pl-5 space-y-2">
              <li>
                Connectez-vous à votre{" "}
                <a
                  href="https://app.supabase.com"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-600 hover:underline"
                >
                  dashboard Supabase
                </a>
              </li>
              <li>Sélectionnez votre projet</li>
              <li>Cliquez sur &quot;Storage&quot; dans le menu de gauche</li>
              <li>Cliquez sur &quot;New Bucket&quot;</li>
              <li>Nommez le bucket &quot;client-files&quot;</li>
              <li>Sélectionnez &quot;Public&quot; pour le type de bucket</li>
              <li>Cliquez sur &quot;Create bucket&quot;</li>
            </ol>

            <h3 className="text-lg font-medium mt-4">Ou créer le bucket via SQL</h3>
            <p>Exécutez le SQL suivant dans l&apos;éditeur SQL de Supabase:</p>
            <pre className="mt-2 p-4 bg-slate-800 text-white rounded text-sm overflow-x-auto">
              {`-- Créer le bucket
INSERT INTO storage.buckets (id, name, public)
VALUES ('client-files', 'client-files', true);

-- Ajouter les politiques d'accès
CREATE POLICY "Allow authenticated users to upload files"
ON storage.objects FOR INSERT TO authenticated USING (
  bucket_id = 'client-files' AND auth.role() = 'authenticated'
);

CREATE POLICY "Allow users to select their own files"
ON storage.objects FOR SELECT TO authenticated USING (
  bucket_id = 'client-files'
);

CREATE POLICY "Allow users to delete their own files"
ON storage.objects FOR DELETE TO authenticated USING (
  bucket_id = 'client-files'
);`}
            </pre>
          </CardContent>
          <CardFooter>
            <Button onClick={checkBucket}>Vérifier à nouveau</Button>
          </CardFooter>
        </Card>
      )}

      {result && result.exists && (
        <Card>
          <CardHeader>
            <CardTitle>Tester l&apos;upload de fichiers</CardTitle>
            <CardDescription>
              Maintenant que le bucket existe, vous pouvez tester l&apos;upload de fichiers.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button onClick={() => router.push("/demandes/nouvelle")}>Créer une nouvelle demande</Button>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
