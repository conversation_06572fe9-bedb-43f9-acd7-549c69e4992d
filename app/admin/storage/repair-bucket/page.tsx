"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { CheckCircle2, AlertCircle, Loader2, Info } from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { useRouter } from "next/navigation"

export default function RepairBucketPage() {
  const { toast } = useToast()
  const router = useRouter()
  const [isRepairing, setIsRepairing] = useState(false)
  const [result, setResult] = useState<any>(null)
  const [error, setError] = useState<string | null>(null)
  const [bucketName, setBucketName] = useState<string>(
    process.env.NEXT_PUBLIC_SUPABASE_CLIENT_FILES_BUCKET || "client-files",
  )

  useEffect(() => {
    // Check if the environment variable is set
    if (!process.env.NEXT_PUBLIC_SUPABASE_CLIENT_FILES_BUCKET) {
      console.warn("NEXT_PUBLIC_SUPABASE_CLIENT_FILES_BUCKET is not set, using default value 'client-files'")
    }
  }, [])

  const repairBucket = async () => {
    setIsRepairing(true)
    setError(null)
    setResult(null)

    try {
      const response = await fetch("/api/storage/fix-bucket", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
      })

      const data = await response.json()
      setResult(data)

      if (data.success) {
        toast({
          title: "Réparation terminée",
          description: data.message,
        })
      } else {
        toast({
          title: "Erreur",
          description: data.error || "Une erreur est survenue lors de la réparation du bucket",
          variant: "destructive",
        })
        setError(data.error || "Une erreur est survenue lors de la réparation du bucket")
      }
    } catch (err) {
      console.error("Error repairing bucket:", err)
      setError("Erreur lors de la réparation du bucket: " + (err.message || "Erreur inconnue"))
      toast({
        title: "Erreur",
        description: "Une erreur est survenue lors de la réparation du bucket",
        variant: "destructive",
      })
    } finally {
      setIsRepairing(false)
    }
  }

  return (
    <div className="container mx-auto py-6">
      <h1 className="text-2xl font-bold mb-6">Réparation du bucket de stockage</h1>

      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Réparer le bucket &apos;{bucketName}&apos;</CardTitle>
          <CardDescription>
            Cette page vous permet de créer ou réparer le bucket de stockage et ses politiques d&apos;accès.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <Alert>
            <Info className="h-4 w-4" />
            <AlertTitle>Configuration actuelle</AlertTitle>
            <AlertDescription>
              <p>
                Bucket configuré: <strong>{bucketName}</strong>
              </p>
              <p className="text-xs text-muted-foreground mt-1">
                (Défini par la variable d&apos;environnement NEXT_PUBLIC_SUPABASE_CLIENT_FILES_BUCKET)
              </p>
            </AlertDescription>
          </Alert>

          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Important</AlertTitle>
            <AlertDescription>
              Cette opération va tenter de créer le bucket &apos;{bucketName}&apos; s&apos;il n&apos;existe pas, ou de
              réparer ses politiques d&apos;accès s&apos;il existe déjà.
            </AlertDescription>
          </Alert>

          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Erreur</AlertTitle>
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {result && result.success && (
            <Alert variant="default">
              <CheckCircle2 className="h-4 w-4 text-green-500" />
              <AlertTitle>Opération réussie</AlertTitle>
              <AlertDescription>{result.message}</AlertDescription>
            </Alert>
          )}

          <Button onClick={repairBucket} disabled={isRepairing}>
            {isRepairing ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Réparation en cours...
              </>
            ) : (
              "Réparer le bucket"
            )}
          </Button>
        </CardContent>
        <CardFooter className="flex justify-between">
          <Button variant="outline" onClick={() => router.push("/admin/storage/verification")}>
            Vérifier le bucket
          </Button>
          {result && result.success && (
            <Button onClick={() => router.push("/demandes/nouvelle")}>Tester l&apos;upload de fichiers</Button>
          )}
        </CardFooter>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Que fait cette réparation ?</CardTitle>
          <CardDescription>Voici les actions effectuées par la réparation du bucket.</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <ol className="list-decimal pl-5 space-y-2">
            <li>Vérification de l&apos;existence du bucket &apos;{bucketName}&apos;</li>
            <li>Création du bucket s&apos;il n&apos;existe pas</li>
            <li>Création ou mise à jour des politiques d&apos;accès suivantes:</li>
            <ul className="list-disc pl-5 space-y-1 mt-1">
              <li>Politique d&apos;upload pour les utilisateurs authentifiés</li>
              <li>Politique de lecture pour les utilisateurs authentifiés</li>
              <li>Politique de mise à jour pour les utilisateurs authentifiés</li>
              <li>Politique de suppression pour les utilisateurs authentifiés</li>
            </ul>
          </ol>
        </CardContent>
      </Card>
    </div>
  )
}
