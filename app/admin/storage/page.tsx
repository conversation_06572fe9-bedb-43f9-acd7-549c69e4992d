"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { AlertCircle, CheckCircle2 } from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { checkStorageBucketExists } from "@/services/requests"

export default function StorageSetupPage() {
  const { toast } = useToast()
  const [isChecking, setIsChecking] = useState(false)
  const [bucketExists, setBucketExists] = useState<boolean | null>(null)

  const checkBucket = async () => {
    setIsChecking(true)
    try {
      const exists = await checkStorageBucketExists()
      setBucketExists(exists)
      toast({
        title: exists ? "Bucket trouvé" : "Bucket non trouvé",
        description: exists
          ? "Le bucket 'client-files' existe dans votre projet Supabase."
          : "Le bucket 'client-files' n'existe pas dans votre projet Supabase.",
        variant: exists ? "default" : "destructive",
      })
    } catch (error) {
      console.error("Erreur lors de la vérification du bucket:", error)
      toast({
        title: "Erreur",
        description: "Impossible de vérifier l'existence du bucket.",
        variant: "destructive",
      })
    } finally {
      setIsChecking(false)
    }
  }

  return (
    <div className="container mx-auto py-6">
      <h1 className="text-2xl font-bold mb-6">Configuration du stockage</h1>

      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Vérification du bucket de stockage</CardTitle>
          <CardDescription>
            Vérifiez si le bucket &apos;client-files&apos; existe dans votre projet Supabase.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Button onClick={checkBucket} disabled={isChecking}>
            {isChecking ? "Vérification en cours..." : "Vérifier le bucket"}
          </Button>

          {bucketExists !== null && (
            <Alert variant={bucketExists ? "default" : "destructive"} className="mt-4">
              {bucketExists ? <CheckCircle2 className="h-4 w-4" /> : <AlertCircle className="h-4 w-4" />}
              <AlertTitle>{bucketExists ? "Bucket trouvé" : "Bucket non trouvé"}</AlertTitle>
              <AlertDescription>
                {bucketExists
                  ? "Le bucket 'client-files' existe dans votre projet Supabase."
                  : "Le bucket 'client-files' n'existe pas dans votre projet Supabase."}
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Création du bucket de stockage</CardTitle>
          <CardDescription>
            Instructions pour créer le bucket &apos;client-files&apos; dans votre projet Supabase.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <p>Pour créer le bucket de stockage, suivez ces étapes:</p>

          <div className="space-y-4">
            <div>
              <h3 className="text-lg font-medium">Option 1: Via l&apos;interface Supabase</h3>
              <ol className="list-decimal pl-5 mt-2 space-y-2">
                <li>
                  Connectez-vous à votre{" "}
                  <a
                    href="https://app.supabase.com"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-primary underline"
                  >
                    dashboard Supabase
                  </a>
                </li>
                <li>Sélectionnez votre projet</li>
                <li>Cliquez sur &quot;Storage&quot; dans le menu de gauche</li>
                <li>Cliquez sur &quot;New Bucket&quot;</li>
                <li>Nommez le bucket &quot;client-files&quot;</li>
                <li>Sélectionnez &quot;Public&quot; pour le type de bucket</li>
                <li>Cliquez sur &quot;Create bucket&quot;</li>
              </ol>
            </div>

            <div>
              <h3 className="text-lg font-medium">Option 2: Via l&apos;éditeur SQL</h3>
              <p className="mt-2">Exécutez ce SQL dans l&apos;éditeur SQL de Supabase:</p>
              <pre className="mt-2 p-4 bg-slate-800 text-white rounded text-sm overflow-x-auto">
                {`-- Créer le bucket de stockage
INSERT INTO storage.buckets (id, name, public)
VALUES ('client-files', 'client-files', true);

-- Ajouter les politiques d'accès
CREATE POLICY "Allow authenticated users to upload files"
ON storage.objects FOR INSERT TO authenticated USING (
  bucket_id = 'client-files' AND auth.role() = 'authenticated'
);

CREATE POLICY "Allow users to select their own files"
ON storage.objects FOR SELECT TO authenticated USING (
  bucket_id = 'client-files'
);

CREATE POLICY "Allow users to update their own files"
ON storage.objects FOR UPDATE TO authenticated USING (
  bucket_id = 'client-files'
);

CREATE POLICY "Allow users to delete their own files"
ON storage.objects FOR DELETE TO authenticated USING (
  bucket_id = 'client-files'
);`}
              </pre>
            </div>
          </div>
        </CardContent>
        <CardFooter>
          <p className="text-sm text-muted-foreground">
            Après avoir créé le bucket, cliquez sur le bouton &quot;Vérifier le bucket&quot; pour confirmer qu&apos;il a
            été créé correctement.
          </p>
        </CardFooter>
      </Card>
    </div>
  )
}
