"use client"

import { useEffect, useState } from "react"
import { getRapports, type Rapport } from "@/services/rapports"
import { formatDate } from "@/lib/utils"
import { Badge } from "@/components/ui/badge"
import Link from "next/link"
import { Skeleton } from "@/components/ui/skeleton"

export function RapportList() {
  const [rapports, setRapports] = useState<Rapport[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  async function loadRapports() {
    setLoading(true)
    try {
      const { data, error } = await getRapports()
      if (error) {
        setError(error)
      } else {
        setRapports(data || [])
      }
    } catch (err) {
      setError("Une erreur est survenue lors du chargement des rapports")
      console.error(err)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    loadRapports()
  }, [])

  if (loading) {
    return (
      <div className="space-y-4">
        {Array.from({ length: 5 }).map((_, index) => (
          <div key={index} className="border rounded-lg p-4">
            <div className="flex justify-between">
              <div>
                <Skeleton className="h-5 w-40 mb-2" />
                <Skeleton className="h-4 w-24" />
              </div>
              <Skeleton className="h-6 w-20" />
            </div>
          </div>
        ))}
      </div>
    )
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
        <p className="font-medium">Erreur</p>
        <p>{error}</p>
        <button onClick={loadRapports} className="mt-2 px-3 py-1 bg-red-100 hover:bg-red-200 rounded-md text-sm">
          Réessayer
        </button>
      </div>
    )
  }

  if (rapports.length === 0) {
    return (
      <div className="text-center py-8 border rounded-lg">
        <p className="text-muted-foreground">Aucun rapport trouvé</p>
        <Link
          href="/rapports/nouveau"
          className="inline-flex h-9 items-center justify-center rounded-md bg-primary px-4 py-2 text-sm font-medium text-primary-foreground mt-4"
        >
          Créer un rapport
        </Link>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {rapports.map((rapport) => (
        <div key={rapport.id} className="border rounded-lg p-4 hover:bg-muted/50 transition-colors">
          <div className="flex justify-between items-start">
            <div>
              <h3 className="font-medium">{rapport.titre}</h3>
              <p className="text-sm text-muted-foreground">
                {rapport.projet?.nom || "Projet associé"} • {formatDate(rapport.date_creation)}
              </p>
              <p className="mt-2 line-clamp-2 text-sm">{rapport.contenu}</p>
            </div>
            <Badge
              variant={rapport.statut === "Terminé" ? "success" : rapport.statut === "En cours" ? "default" : "outline"}
            >
              {rapport.statut}
            </Badge>
          </div>
          <div className="mt-4 flex gap-2 justify-end">
            <Link
              href={`/rapports/${rapport.id}`}
              className="text-xs px-2 py-1 rounded-md bg-primary text-primary-foreground hover:bg-primary/90"
            >
              Voir
            </Link>
            <Link
              href={`/rapports/${rapport.id}/modifier`}
              className="text-xs px-2 py-1 rounded-md border hover:bg-muted"
            >
              Modifier
            </Link>
          </div>
        </div>
      ))}
    </div>
  )
}
