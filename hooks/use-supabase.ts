"use client"

import { supabase } from "@/lib/supabase"
import { isDemoMode } from "@/lib/demo-mode"

export function useSupabase() {
  // Si nous sommes en mode démo, retourner un objet factice
  if (isDemoMode()) {
    return {
      // Méthodes factices pour l'authentification
      auth: {
        signInWithPassword: async () => ({ error: new Error("Mode démo actif") }),
        signUp: async () => ({ error: new Error("Mode démo actif") }),
        signOut: async () => ({ error: null }),
        getSession: async () => ({ data: { session: null } }),
      },
      // Méthodes factices pour les requêtes
      from: () => ({
        select: () => ({ data: null, error: new Error("Mode démo actif") }),
        insert: () => ({ data: null, error: new Error("Mode démo actif") }),
        update: () => ({ data: null, error: new Error("Mode démo actif") }),
        delete: () => ({ data: null, error: new Error("Mode démo actif") }),
      }),
    }
  }

  return supabase
}
