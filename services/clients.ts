import { supabase } from "@/lib/supabase"
import { demoData, isDemoMode } from "@/lib/demo-mode"

export interface Client {
  id: number
  entreprise_id: number
  type: "particulier" | "professionnel"
  nom: string
  prenom: string
  societe: string
  siret: string
  adresse_facturation: string
  adresse_chantier: string
  telephone: string
  email: string
  notes: string
  date_creation: string
  actif: boolean
}

export async function getClients() {
  // Si le mode démo est activé, retourner les données de démonstration
  if (isDemoMode()) {
    console.log("Mode démo activé, utilisation des données de démonstration")
    return demoData.clients as Client[]
  }

  try {
    const { data, error } = await supabase.from("clients").select("*").order("date_creation", { ascending: false })

    if (error) {
      console.error("Erreur Supabase:", error)
      throw error
    }

    return data as Client[]
  } catch (error) {
    console.error("Erreur lors de la récupération des clients:", error)
    throw error
  }
}

export async function getClient(id: number) {
  // Si le mode démo est activé, retourner les données de démonstration
  if (isDemoMode()) {
    const client = demoData.clients.find((c) => c.id === id)
    if (!client) {
      throw new Error("Client non trouvé")
    }
    return client as Client
  }

  try {
    const { data, error } = await supabase.from("clients").select("*").eq("id", id).single()

    if (error) {
      console.error("Erreur Supabase:", error)
      throw error
    }

    return data as Client
  } catch (error) {
    console.error("Erreur lors de la récupération du client:", error)
    throw error
  }
}

export async function createClient(client: Omit<Client, "id" | "date_creation">) {
  // Si le mode démo est activé, simuler la création
  if (isDemoMode()) {
    console.log("Mode démo activé, simulation de création de client")
    const clients = [...demoData.clients]
    const newClient: Client = {
      id: clients.length + 1,
      ...client,
      date_creation: new Date().toISOString(),
    }
    return newClient
  }

  try {
    const { data, error } = await supabase.from("clients").insert([client]).select()

    if (error) {
      console.error("Erreur Supabase:", error)
      throw error
    }

    return data[0] as Client
  } catch (error) {
    console.error("Erreur lors de la création du client:", error)
    throw error
  }
}

export async function updateClient(id: number, client: Partial<Client>) {
  // Si le mode démo est activé, simuler la mise à jour
  if (isDemoMode()) {
    console.log("Mode démo activé, simulation de mise à jour de client")
    const clients = [...demoData.clients]
    const index = clients.findIndex((c) => c.id === id)
    if (index === -1) {
      throw new Error("Client non trouvé")
    }
    const updatedClient = { ...clients[index], ...client }
    return updatedClient as Client
  }

  try {
    const { data, error } = await supabase.from("clients").update(client).eq("id", id).select()

    if (error) {
      console.error("Erreur Supabase:", error)
      throw error
    }

    return data[0] as Client
  } catch (error) {
    console.error("Erreur lors de la mise à jour du client:", error)
    throw error
  }
}

export async function deleteClient(id: number) {
  // Si le mode démo est activé, simuler la suppression
  if (isDemoMode()) {
    console.log("Mode démo activé, simulation de suppression de client")
    return true
  }

  try {
    const { error } = await supabase.from("clients").delete().eq("id", id)

    if (error) {
      console.error("Erreur Supabase:", error)
      throw error
    }

    return true
  } catch (error) {
    console.error("Erreur lors de la suppression du client:", error)
    throw error
  }
}
