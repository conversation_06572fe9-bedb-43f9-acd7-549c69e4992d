import { supabase } from "@/lib/supabase"
import { createClient } from "@supabase/supabase-js"
import { demoData, isDemoMode } from "@/lib/demo-mode"

// Create service role client only if the key is available
function createServiceRoleClient() {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

  if (!supabaseUrl || !supabaseServiceKey) {
    console.warn("Service role key not available, using regular client")
    return null
  }

  try {
    return createClient(supabaseUrl, supabaseServiceKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false,
      },
    })
  } catch (error) {
    console.error("Error creating service role client:", error)
    return null
  }
}

// Get the appropriate client (service role if available, otherwise regular client)
function getSupabaseClient() {
  const serviceClient = createServiceRoleClient()
  return serviceClient || supabase
}

export interface Devis {
  id: number
  projet_id: number
  utilisateur_id: number
  reference: string
  date_creation: string
  date_validite: string
  date_signature: string | null
  statut: "brouillon" | "envoyé" | "signé" | "refusé"
  introduction: string
  conclusion: string
  taux_remise: number
  montant_ht: number
  montant_ttc: number
  validite_jours: number
  conditions_acceptees: boolean
  signature_url: string | null
  sections?: SectionDevis[]
}

export interface SectionDevis {
  id: number
  devis_id: number
  titre: string
  description: string
  ordre: number
  montant_ht: number
  lignes?: LigneDevis[]
}

export interface LigneDevis {
  id: number
  section_id: number
  catalogue_item_id: number | null
  designation: string
  reference: string
  description: string
  quantite: number
  unite: string
  prix_unitaire_ht: number
  taux_tva: number
  montant_ht: number
  montant_ttc: number
  ordre: number
}

export async function getDevis() {
  // Si le mode démo est activé, retourner les données de démonstration
  if (isDemoMode()) {
    console.log("Mode démo activé, utilisation des données de démonstration")
    return demoData.devis as Devis[]
  }

  console.log("🔍 Fetching devis from database...")

  const client = getSupabaseClient()
  if (!client) {
    console.error("❌ No Supabase client available")
    return []
  }

  try {
    // First, let's check if we can access the table at all
    console.log("📊 Checking table access...")
    const { count, error: countError } = await client.from("devis").select("*", { count: "exact", head: true })

    if (countError) {
      console.error("❌ Error checking table access:", countError)
    } else {
      console.log(`📈 Found ${count} devis in table`)
    }

    // Now fetch the actual data
    console.log("📥 Fetching devis data...")
    const { data, error } = await client.from("devis").select("*").order("date_creation", { ascending: false })

    if (error) {
      console.error("❌ Erreur Supabase:", error)
      console.error("Error details:", {
        message: error.message,
        details: error.details,
        hint: error.hint,
        code: error.code,
      })
      throw error
    }

    console.log("✅ Successfully fetched devis:", data?.length || 0, "items")
    console.log("📋 First devis sample:", data?.[0])

    return data as Devis[]
  } catch (error) {
    console.error("💥 Erreur lors de la récupération des devis:", error)
    throw error
  }
}

export async function getDevisById(id: number) {
  // Si le mode démo est activé, retourner les données de démonstration
  if (isDemoMode()) {
    const devis = demoData.devis.find((d) => d.id === id)
    if (!devis) {
      throw new Error("Devis non trouvé")
    }
    return devis as Devis
  }

  console.log(`🔍 Fetching devis with ID: ${id}`)

  const client = getSupabaseClient()
  if (!client) {
    throw new Error("No Supabase client available")
  }

  try {
    // Fetch devis with sections and lines
    const { data: devisData, error: devisError } = await client.from("devis").select("*").eq("id", id).single()

    if (devisError) {
      console.error("❌ Erreur Supabase devis:", devisError)
      throw devisError
    }

    // Fetch sections for this devis
    const { data: sectionsData, error: sectionsError } = await client
      .from("sections_devis")
      .select("*")
      .eq("devis_id", id)
      .order("ordre", { ascending: true })

    if (sectionsError) {
      console.error("❌ Erreur Supabase sections:", sectionsError)
      // Don't throw, just log the error and continue without sections
      console.warn("Continuing without sections data")
    }

    // Fetch lines for each section
    const sectionsWithLines = await Promise.all(
      (sectionsData || []).map(async (section) => {
        const { data: lignesData, error: lignesError } = await client
          .from("lignes_devis")
          .select("*")
          .eq("section_id", section.id)
          .order("ordre", { ascending: true })

        if (lignesError) {
          console.error(`❌ Erreur Supabase lignes for section ${section.id}:`, lignesError)
          return { ...section, lignes: [] }
        }

        return { ...section, lignes: lignesData || [] }
      }),
    )

    const devis = {
      ...devisData,
      sections: sectionsWithLines,
    }

    console.log("✅ Successfully fetched devis with sections:", devis)
    return devis as Devis
  } catch (error) {
    console.error("💥 Erreur lors de la récupération du devis:", error)
    throw error
  }
}

export async function createDevis(devis: Omit<Devis, "id" | "date_creation">) {
  // Si le mode démo est activé, simuler la création
  if (isDemoMode()) {
    console.log("Mode démo activé, simulation de création de devis")
    const devisList = [...demoData.devis]
    const newDevis: Devis = {
      id: devisList.length + 1,
      ...devis,
      date_creation: new Date().toISOString(),
    }
    return newDevis
  }

  const client = getSupabaseClient()
  if (!client) {
    throw new Error("No Supabase client available")
  }

  try {
    const { data, error } = await client.from("devis").insert([devis]).select()

    if (error) {
      console.error("Erreur Supabase:", error)
      throw error
    }

    return data[0] as Devis
  } catch (error) {
    console.error("Erreur lors de la création du devis:", error)
    throw error
  }
}

export async function updateDevis(id: number, devis: Partial<Devis>) {
  // Si le mode démo est activé, simuler la mise à jour
  if (isDemoMode()) {
    console.log("Mode démo activé, simulation de mise à jour de devis")
    const devisList = [...demoData.devis]
    const index = devisList.findIndex((d) => d.id === id)
    if (index === -1) {
      throw new Error("Devis non trouvé")
    }
    const updatedDevis = { ...devisList[index], ...devis }
    return updatedDevis as Devis
  }

  const client = getSupabaseClient()
  if (!client) {
    throw new Error("No Supabase client available")
  }

  try {
    const { data, error } = await client.from("devis").update(devis).eq("id", id).select()

    if (error) {
      console.error("Erreur Supabase:", error)
      throw error
    }

    return data[0] as Devis
  } catch (error) {
    console.error("Erreur lors de la mise à jour du devis:", error)
    throw error
  }
}

export async function deleteDevis(id: number) {
  // Si le mode démo est activé, simuler la suppression
  if (isDemoMode()) {
    console.log("Mode démo activé, simulation de suppression de devis")
    return true
  }

  const client = getSupabaseClient()
  if (!client) {
    throw new Error("No Supabase client available")
  }

  try {
    const { error } = await client.from("devis").delete().eq("id", id)

    if (error) {
      console.error("Erreur Supabase:", error)
      throw error
    }

    return true
  } catch (error) {
    console.error("Erreur lors de la suppression du devis:", error)
    throw error
  }
}

// Fonctions pour les sections et lignes de devis
export async function getSectionsDevis(devisId: number) {
  if (isDemoMode()) {
    return [] as SectionDevis[]
  }

  const client = getSupabaseClient()
  if (!client) {
    return []
  }

  try {
    const { data, error } = await client
      .from("sections_devis")
      .select("*")
      .eq("devis_id", devisId)
      .order("ordre", { ascending: true })

    if (error) {
      console.error("Erreur Supabase:", error)
      throw error
    }

    return data as SectionDevis[]
  } catch (error) {
    console.error("Erreur lors de la récupération des sections:", error)
    throw error
  }
}

export async function getLignesDevis(sectionId: number) {
  if (isDemoMode()) {
    return [] as LigneDevis[]
  }

  const client = getSupabaseClient()
  if (!client) {
    return []
  }

  try {
    const { data, error } = await client
      .from("lignes_devis")
      .select("*")
      .eq("section_id", sectionId)
      .order("ordre", { ascending: true })

    if (error) {
      console.error("Erreur Supabase:", error)
      throw error
    }

    return data as LigneDevis[]
  } catch (error) {
    console.error("Erreur lors de la récupération des lignes:", error)
    throw error
  }
}

export async function createSectionDevis(section: Omit<SectionDevis, "id">) {
  if (isDemoMode()) {
    return {
      id: Date.now(),
      ...section,
      lignes: [],
    } as SectionDevis
  }

  const client = getSupabaseClient()
  if (!client) {
    throw new Error("No Supabase client available")
  }

  try {
    const { data, error } = await client.from("sections_devis").insert([section]).select()

    if (error) {
      console.error("Erreur Supabase:", error)
      throw error
    }

    return data[0] as SectionDevis
  } catch (error) {
    console.error("Erreur lors de la création de la section:", error)
    throw error
  }
}

export async function updateSectionDevis(id: number, section: Partial<SectionDevis>) {
  if (isDemoMode()) {
    return {
      id,
      ...section,
    } as SectionDevis
  }

  const client = getSupabaseClient()
  if (!client) {
    throw new Error("No Supabase client available")
  }

  try {
    const { data, error } = await client.from("sections_devis").update(section).eq("id", id).select()

    if (error) {
      console.error("Erreur Supabase:", error)
      throw error
    }

    return data[0] as SectionDevis
  } catch (error) {
    console.error("Erreur lors de la mise à jour de la section:", error)
    throw error
  }
}

export async function deleteSectionDevis(id: number) {
  if (isDemoMode()) {
    return true
  }

  const client = getSupabaseClient()
  if (!client) {
    throw new Error("No Supabase client available")
  }

  try {
    const { error } = await client.from("sections_devis").delete().eq("id", id)

    if (error) {
      console.error("Erreur Supabase:", error)
      throw error
    }

    return true
  } catch (error) {
    console.error("Erreur lors de la suppression de la section:", error)
    throw error
  }
}

export async function createLigneDevis(ligne: Omit<LigneDevis, "id">) {
  if (isDemoMode()) {
    return {
      id: Date.now(),
      ...ligne,
    } as LigneDevis
  }

  const client = getSupabaseClient()
  if (!client) {
    throw new Error("No Supabase client available")
  }

  try {
    const { data, error } = await client.from("lignes_devis").insert([ligne]).select()

    if (error) {
      console.error("Erreur Supabase:", error)
      throw error
    }

    return data[0] as LigneDevis
  } catch (error) {
    console.error("Erreur lors de la création de la ligne:", error)
    throw error
  }
}

export async function updateLigneDevis(id: number, ligne: Partial<LigneDevis>) {
  if (isDemoMode()) {
    return {
      id,
      ...ligne,
    } as LigneDevis
  }

  const client = getSupabaseClient()
  if (!client) {
    throw new Error("No Supabase client available")
  }

  try {
    const { data, error } = await client.from("lignes_devis").update(ligne).eq("id", id).select()

    if (error) {
      console.error("Erreur Supabase:", error)
      throw error
    }

    return data[0] as LigneDevis
  } catch (error) {
    console.error("Erreur lors de la mise à jour de la ligne:", error)
    throw error
  }
}

export async function deleteLigneDevis(id: number) {
  if (isDemoMode()) {
    return true
  }

  const client = getSupabaseClient()
  if (!client) {
    throw new Error("No Supabase client available")
  }

  try {
    const { error } = await client.from("lignes_devis").delete().eq("id", id)

    if (error) {
      console.error("Erreur Supabase:", error)
      throw error
    }

    return true
  } catch (error) {
    console.error("Erreur lors de la suppression de la ligne:", error)
    throw error
  }
}
