import { supabase } from "@/lib/supabase"
import { demoData, isDemoMode } from "@/lib/demo-mode"

export interface Entreprise {
  id: number
  nom: string
  siret: string | null
  adresse: string | null
  code_postal: string | null
  ville: string | null
  telephone: string | null
  email: string | null
  logo_url: string | null
  site_web: string | null
  cgv: string | null
  date_creation: string
  devise: string
  actif: boolean
}

export async function getEntreprises() {
  // Si le mode démo est activé, retourner les données de démonstration
  if (isDemoMode()) {
    console.log("Mode démo activé, utilisation des données de démonstration")
    return demoData.entreprises as Entreprise[]
  }

  try {
    const { data, error } = await supabase.from("entreprises").select("*")

    if (error) {
      console.error("Erreur Supabase:", error)
      throw error
    }

    return data as Entreprise[]
  } catch (error) {
    console.error("Erreur lors de la récupération des entreprises:", error)
    throw error
  }
}

export async function getEntreprise(id: number) {
  // Si le mode démo est activé, retourner les données de démonstration
  if (isDemoMode()) {
    const entreprise = demoData.entreprises.find((e) => e.id === id)
    if (!entreprise) {
      throw new Error("Entreprise non trouvée")
    }
    return entreprise as Entreprise
  }

  try {
    const { data, error } = await supabase.from("entreprises").select("*").eq("id", id).single()

    if (error) {
      console.error("Erreur Supabase:", error)
      throw error
    }

    return data as Entreprise
  } catch (error) {
    console.error("Erreur lors de la récupération de l'entreprise:", error)
    throw error
  }
}

export async function createEntreprise(entreprise: Omit<Entreprise, "id" | "date_creation">) {
  // Si le mode démo est activé, simuler la création
  if (isDemoMode()) {
    console.log("Mode démo activé, simulation de création d'entreprise")
    const newEntreprise: Entreprise = {
      id: 1,
      ...entreprise,
      date_creation: new Date().toISOString(),
    }
    return newEntreprise
  }

  try {
    const { data, error } = await supabase.from("entreprises").insert([entreprise]).select()

    if (error) {
      console.error("Erreur Supabase:", error)
      throw error
    }

    return data[0] as Entreprise
  } catch (error) {
    console.error("Erreur lors de la création de l'entreprise:", error)
    throw error
  }
}

export async function updateEntreprise(id: number, entreprise: Partial<Entreprise>) {
  // Si le mode démo est activé, simuler la mise à jour
  if (isDemoMode()) {
    console.log("Mode démo activé, simulation de mise à jour d'entreprise")
    const entreprises = [...demoData.entreprises]
    const index = entreprises.findIndex((e) => e.id === id)
    if (index === -1) {
      throw new Error("Entreprise non trouvée")
    }
    const updatedEntreprise = { ...entreprises[index], ...entreprise }
    return updatedEntreprise as Entreprise
  }

  try {
    const { data, error } = await supabase.from("entreprises").update(entreprise).eq("id", id).select()

    if (error) {
      console.error("Erreur Supabase:", error)
      throw error
    }

    return data[0] as Entreprise
  } catch (error) {
    console.error("Erreur lors de la mise à jour de l'entreprise:", error)
    throw error
  }
}

export async function deleteEntreprise(id: number) {
  // Si le mode démo est activé, simuler la suppression
  if (isDemoMode()) {
    console.log("Mode démo activé, simulation de suppression d'entreprise")
    return true
  }

  try {
    const { error } = await supabase.from("entreprises").delete().eq("id", id)

    if (error) {
      console.error("Erreur Supabase:", error)
      throw error
    }

    return true
  } catch (error) {
    console.error("Erreur lors de la suppression de l'entreprise:", error)
    throw error
  }
}

// Updated function to handle multiple or no enterprises
export async function getUserEnterprise() {
  // Si le mode démo est activé, retourner les données de démonstration
  if (isDemoMode()) {
    console.log("Mode démo activé, utilisation des données de démonstration")
    return {
      id: 1,
      nom: "BTP Expert",
      siret: "12345678901234",
      adresse: "123 rue des Artisans",
      code_postal: "75001",
      ville: "Paris",
      telephone: "01 23 45 67 89",
      email: "<EMAIL>",
      logo_url: null,
      site_web: "www.btpexpert.fr",
      cgv: null,
      date_creation: new Date().toISOString(),
      devise: "EUR",
      actif: true,
    } as Entreprise
  }

  try {
    // Get all enterprises without using .single()
    const { data, error } = await supabase.from("entreprises").select("*")

    if (error) {
      console.error("Erreur lors de la récupération des entreprises:", error)
      throw error
    }

    // If there are enterprises, return the first one
    if (data && data.length > 0) {
      return data[0] as Entreprise
    }

    // If there are no enterprises, create a default one
    console.log("Aucune entreprise trouvée, création d'une entreprise par défaut")

    const defaultEnterprise = {
      nom: "BTP Expert",
      siret: "12345678901234",
      adresse: "123 rue des Artisans",
      code_postal: "75001",
      ville: "Paris",
      telephone: "01 23 45 67 89",
      email: "<EMAIL>",
      site_web: "www.btpexpert.fr",
      devise: "EUR",
      actif: true,
    }

    const { data: newEnterprise, error: createError } = await supabase
      .from("entreprises")
      .insert([defaultEnterprise])
      .select()

    if (createError) {
      console.error("Erreur lors de la création de l'entreprise par défaut:", createError)
      // Return a default enterprise with ID 1 even if creation fails
      return {
        id: 1,
        ...defaultEnterprise,
        date_creation: new Date().toISOString(),
        logo_url: null,
        cgv: null,
      } as Entreprise
    }

    return newEnterprise[0] as Entreprise
  } catch (error) {
    console.error("Erreur lors de la récupération de l'entreprise:", error)
    // Return a default enterprise with ID 1 in case of any error
    return {
      id: 1,
      nom: "BTP Expert",
      siret: "12345678901234",
      adresse: "123 rue des Artisans",
      code_postal: "75001",
      ville: "Paris",
      telephone: "01 23 45 67 89",
      email: "<EMAIL>",
      logo_url: null,
      site_web: "www.btpexpert.fr",
      cgv: null,
      date_creation: new Date().toISOString(),
      devise: "EUR",
      actif: true,
    } as Entreprise
  }
}
