import { supabase } from "@/lib/supabase"
import { createClient } from "@supabase/supabase-js"
import { isDemoMode } from "@/lib/demo-mode"
import { generateText, generateObject } from "ai"
import { aiConfig, handleAIError } from "@/lib/ai-config"
import { z } from "zod"

export interface Agent {
  id: number
  request_id: number
  agent_response: string
  created_at: string
  updated_at: string
}

// Get the bucket name from environment variable
const BUCKET_NAME = process.env.NEXT_PUBLIC_SUPABASE_CLIENT_FILES_BUCKET || "client-files"

// Create a service role client that bypasses RLS
function createServiceRoleClient() {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

  if (!supabaseUrl || !supabaseServiceKey) {
    throw new Error("Variables d'environnement Supabase manquantes pour le service role")
  }

  return createClient(supabaseUrl, supabaseServiceKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false,
    },
  })
}

// Enhanced system prompt for AI analysis with Gemini 2.5 Pro capabilities
const ANALYSIS_SYSTEM_PROMPT = `## System Prompt: Assistant de Gestion de Projets BTP - Gemini 2.5 Pro

Tu es un assistant IA de nouvelle génération spécialisé dans la gestion de projets du secteur BTP (Bâtiment et Travaux Publics). Utilise tes capacités avancées de raisonnement et d'analyse pour fournir des insights détaillés et des recommandations pratiques.

### Capacités Avancées:
- Analyse multimodale des documents (texte, images, plans)
- Raisonnement complexe sur les contraintes techniques et réglementaires
- Optimisation des ressources et planification intelligente
- Détection proactive des risques et opportunités

### 1. Points d'Attention Critiques
- Identifie et analyse les risques potentiels avec une évaluation de probabilité et d'impact
- Évalue les points de vigilance réglementaires (RT 2012, RE 2020, accessibilité PMR, etc.)
- Signale les éléments techniques nécessitant une attention particulière
- Propose des solutions de mitigation hiérarchisées par efficacité

### 2. Suggestions d'Implantation Optimisées
- Recommande l'organisation optimale basée sur les flux de travail
- Propose des configurations efficientes avec calculs d'optimisation
- Analyse les contraintes géotechniques et environnementales
- Optimise les accès, stockage et zones de travail

### 3. Planification Matérielle Intelligente
- Établis des listes détaillées avec quantifications précises
- Planifie les approvisionnements selon le planning critique
- Classe les matériaux par criticité et délais d'approvisionnement
- Indique les spécifications techniques et normes applicables

### 4. Recherche de Fournisseurs et Optimisation Commerciale
- Identifie les types de fournisseurs avec critères de sélection
- Suggère des stratégies d'achat groupé et négociation
- Analyse les tendances de marché et saisonnalité
- Propose des alternatives et substitutions possibles

### 5. Gestion RH et Coordination Avancée
- Évalue les besoins en compétences avec matrice de compétences
- Propose des structures d'équipe optimisées
- Identifie les goulots d'étranglement en ressources humaines
- Recommande des approches de formation et montée en compétences

### Format de Réponse:
Utilise un format structuré avec:
- Titres et sous-titres clairs
- Listes à puces hiérarchisées
- Données chiffrées et métriques quand pertinent
- Codes couleur conceptuels (🔴 Critique, 🟡 Attention, 🟢 Optimal)
- Vocabulaire technique précis du BTP

Si des informations sont manquantes, indique-le clairement et suggère les données complémentaires nécessaires.`

// Zod schema for devis structure validation
const DevisLineSchema = z.object({
  designation: z.string().min(1, "La désignation est requise"),
  description: z.string().default(""),
  quantite: z.number().positive().default(1),
  unite: z.string().default("forfait"),
  prix_unitaire_ht: z.number().nullable().default(null),
  taux_tva: z.number().min(0).max(100).default(20),
})

const DevisSectionSchema = z.object({
  titre_section: z.string().min(1, "Le titre de section est requis"),
  description: z.string().default(""),
  lignes_devis: z.array(DevisLineSchema).min(1, "Au moins une ligne de devis est requise"),
})

const DevisStructureSchema = z.object({
  sections: z.array(DevisSectionSchema).min(1, "Au moins une section est requise"),
})

// Enhanced system prompt for converting analysis to devis with structured output
const DEVIS_CONVERSION_PROMPT = `Tu es un assistant IA spécialisé dans la transformation d'analyses de projets BTP en structures de devis détaillées et professionnelles.

Analyse le texte fourni et extrais tous les éléments quantifiables pour créer une structure de devis organisée selon les lots BTP standards.

Sections Standards BTP à utiliser selon le contexte:
- Préparation et Installation de Chantier
- Gros Œuvre (fondations, structure, maçonnerie)
- Charpente et Couverture
- Étanchéité et Isolation
- Cloisons et Doublages
- Menuiseries Extérieures
- Plomberie et Chauffage
- Électricité
- Revêtements de Sol
- Peinture et Finitions
- Menuiseries Intérieures
- Équipements et Accessoires
- Espaces Verts et VRD
- Main d'Œuvre Spécialisée

Pour chaque élément identifiable:
- Crée une désignation claire et professionnelle
- Fournis une description technique détaillée
- Estime une quantité réaliste basée sur l'analyse
- Choisis l'unité appropriée (m², m³, ml, u, forfait, h, j, etc.)
- Laisse le prix unitaire à null (sera rempli par l'utilisateur)
- Utilise un taux de TVA de 20% par défaut (10% pour certains travaux de rénovation)`

// Get agent analysis for a request
export async function getAgentAnalysis(requestId: number): Promise<Agent | null> {
  if (isDemoMode()) {
    return {
      id: 1,
      request_id: requestId,
      agent_response: "Analyse de démonstration pour la demande #" + requestId,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    }
  }

  try {
    const { data, error } = await supabase.from("agent").select("*").eq("request_id", requestId).single()

    if (error && error.code !== "PGRST116") {
      console.error("Erreur lors de la récupération de l'analyse:", error)
      throw error
    }

    return data as Agent | null
  } catch (error) {
    console.error("Erreur lors de la récupération de l'analyse:", error)
    throw error
  }
}

// Convert file to base64 for AI processing
async function fileToBase64(filePath: string): Promise<string> {
  try {
    const { data, error } = await supabase.storage.from(BUCKET_NAME).download(filePath)

    if (error) {
      console.error("Erreur lors du téléchargement du fichier:", error)
      throw error
    }

    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = () => {
        const base64 = reader.result as string
        // Remove the data URL prefix to get just the base64 string
        const base64Data = base64.split(",")[1]
        resolve(base64Data)
      }
      reader.onerror = reject
      reader.readAsDataURL(data)
    })
  } catch (error) {
    console.error("Erreur lors de la conversion en base64:", error)
    throw error
  }
}

// Analyze request with AI using Gemini 2.5 Pro
export async function analyzeRequestWithAI(requestId: number): Promise<Agent> {
  if (isDemoMode()) {
    console.log("Mode démo: simulation d'analyse IA")
    return {
      id: 1,
      request_id: requestId,
      agent_response: `# Analyse IA Gemini 2.5 Pro - Demande #${requestId}

## 🔍 Analyse Avancée du Projet

### Points d'Attention Critiques
🔴 **Risques Élevés:**
- Vérification de la conformité réglementaire RT 2020
- Analyse des risques de sécurité sur site
- Contrôle de la faisabilité technique structurelle

🟡 **Points de Vigilance:**
- Coordination des corps d'état
- Respect des délais d'approvisionnement
- Gestion des interfaces techniques

### Suggestions d'Implantation Optimisées
🟢 **Configuration Recommandée:**
- Zone de stockage matériaux: 150m² minimum
- Accès engins: largeur 4m, rayon de braquage 12m
- Base vie: 30m² pour équipe de 8 personnes
- Évacuation des déchets: benne 20m³

### Planification Matérielle Intelligente
**Gros Œuvre:**
- Béton C25/30: 50m³ ±10%
- Acier HA: 2,5 tonnes
- Coffrages: 200m² de surface

**Équipements:**
- Grue mobile 25T: 5 jours
- Bétonnière 350L: location mensuelle
- Équipements de sécurité: 8 personnes

### Optimisation Commerciale
**Fournisseurs Prioritaires:**
- Centrale à béton locale (rayon 30km)
- Négoce matériaux avec livraison
- Loueur d'engins certifié

**Stratégies d'Achat:**
- Commande groupée béton: -8% sur prix unitaire
- Négociation forfait location: -15% sur tarif journalier

### Gestion RH Avancée
**Équipe Optimale:**
- Chef de chantier: 1 (niveau II minimum)
- Maçons qualifiés: 3 (CAP + 5 ans exp.)
- Manœuvres: 2
- Coffreur-ferrailleur: 2

**Planning Prévisionnel:** 4 semaines ±1 semaine`,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    }
  }

  try {
    // Get request data with plans and photos
    const { data: request, error: requestError } = await supabase
      .from("requests")
      .select(`
        *,
        client:clients(id, nom, prenom, societe, type)
      `)
      .eq("id", requestId)
      .single()

    if (requestError) {
      console.error("Erreur lors de la récupération de la demande:", requestError)
      throw requestError
    }

    // Get plans
    const { data: plans, error: plansError } = await supabase.from("plans").select("*").eq("request_id", requestId)

    if (plansError) {
      console.error("Erreur lors de la récupération des plans:", plansError)
      throw plansError
    }

    // Get photos
    const { data: photos, error: photosError } = await supabase.from("photos").select("*").eq("request_id", requestId)

    if (photosError) {
      console.error("Erreur lors de la récupération des photos:", photosError)
      throw photosError
    }

    // Prepare enhanced content for AI
    let content = `## Analyse de Projet BTP - Demande #${requestId}

### Description du Projet:
${request.description}

### Informations Client:
`

    if (request.client) {
      content += `- **Type:** ${request.client.type}
- **Nom:** ${
        request.client.type === "particulier"
          ? `${request.client.prenom} ${request.client.nom}`
          : request.client.societe
      }
`
    }

    // Add information about plans and photos with enhanced context
    if (plans && plans.length > 0) {
      content += `
### Documents Plans Disponibles (${plans.length}):
`
      plans.forEach((plan, index) => {
        content += `${index + 1}. **${plan.file_name}** (${plan.file_type})
   - Description: ${plan.description || "Non spécifiée"}
   - Date d'upload: ${new Date(plan.uploaded_at).toLocaleDateString("fr-FR")}
`
      })
    }

    if (photos && photos.length > 0) {
      content += `
### Photos Disponibles (${photos.length}):
`
      photos.forEach((photo, index) => {
        content += `${index + 1}. **${photo.file_name}**
   - Description: ${photo.description || "Non spécifiée"}
   - Date d'upload: ${new Date(photo.uploaded_at).toLocaleDateString("fr-FR")}
`
      })
    }

    content += `

### Instructions d'Analyse:
Effectue une analyse complète et détaillée de ce projet BTP en utilisant tes capacités avancées de raisonnement. Fournis des recommandations concrètes et chiffrées basées sur les bonnes pratiques du secteur.`

    // Call Gemini 2.5 Pro with enhanced settings
    const { text } = await generateText({
      model: aiConfig.model,
      system: ANALYSIS_SYSTEM_PROMPT,
      prompt: content,
      ...aiConfig.analysisSettings,
    })

    // Store the analysis result
    const { data: existingAnalysis } = await supabase.from("agent").select("id").eq("request_id", requestId).single()

    let agentData: Agent

    if (existingAnalysis) {
      // Update existing analysis
      const { data, error } = await supabase
        .from("agent")
        .update({
          agent_response: text,
          updated_at: new Date().toISOString(),
        })
        .eq("request_id", requestId)
        .select()
        .single()

      if (error) {
        console.error("Erreur lors de la mise à jour de l'analyse:", error)
        throw error
      }

      agentData = data as Agent
    } else {
      // Create new analysis
      const { data, error } = await supabase
        .from("agent")
        .insert([
          {
            request_id: requestId,
            agent_response: text,
          },
        ])
        .select()
        .single()

      if (error) {
        console.error("Erreur lors de la création de l'analyse:", error)
        throw error
      }

      agentData = data as Agent
    }

    return agentData
  } catch (error: any) {
    console.error("Erreur lors de l'analyse IA:", error)

    // Use enhanced error handling
    const errorMessage = handleAIError(error)
    throw new Error(errorMessage)
  }
}

// Convert AI analysis to devis structure using Gemini 2.5 Pro with structured output
export async function convertAnalysisToDevis(requestId: number, userId: number): Promise<any> {
  if (isDemoMode()) {
    console.log("Mode démo: simulation de conversion en devis")
    return { id: 1, message: "Devis créé en mode démo avec Gemini 2.5 Pro" }
  }

  try {
    // Get the AI analysis
    const analysis = await getAgentAnalysis(requestId)
    if (!analysis) {
      throw new Error("Aucune analyse IA trouvée pour cette demande")
    }

    // Use service role client to bypass RLS
    const serviceSupabase = createServiceRoleClient()

    // Verify that the user exists
    const { data: userExists, error: userCheckError } = await serviceSupabase
      .from("utilisateurs")
      .select("id")
      .eq("id", userId)
      .single()

    if (userCheckError || !userExists) {
      console.error("Utilisateur non trouvé:", userId, userCheckError)
      throw new Error(`Utilisateur avec l'ID ${userId} non trouvé`)
    }

    // Enhanced prompt for devis conversion
    const conversionPrompt = `## Conversion d'Analyse BTP en Structure de Devis

### Analyse Source:
${analysis.agent_response}

### Instructions:
Convertis cette analyse en structure de devis professionnelle. Extrais TOUS les éléments quantifiables et organise-les selon les lots BTP standards. Assure-toi que chaque ligne de devis soit précise et professionnelle.`

    let devisStructure

    try {
      // Try using generateObject for structured output (more reliable)
      console.log("Tentative de génération structurée avec generateObject...")

      const { object } = await generateObject({
        model: aiConfig.model,
        system: DEVIS_CONVERSION_PROMPT,
        prompt: conversionPrompt,
        schema: DevisStructureSchema,
        ...aiConfig.structuredSettings,
      })

      devisStructure = object
      console.log("Génération structurée réussie")
    } catch (structuredError) {
      console.warn("Échec de la génération structurée, tentative de fallback:", structuredError)

      // Fallback: create a basic structure from the analysis text
      devisStructure = createFallbackDevisStructure(analysis.agent_response)
    }

    // Validate the structure
    const validationResult = DevisStructureSchema.safeParse(devisStructure)
    if (!validationResult.success) {
      console.warn("Structure invalide, utilisation du fallback:", validationResult.error)
      devisStructure = createFallbackDevisStructure(analysis.agent_response)
    }

    // Check if a devis already exists for this request
    const { data: existingDevis } = await serviceSupabase
      .from("devis")
      .select("id")
      .eq("request_id", requestId)
      .single()

    let devisId: number

    if (existingDevis) {
      // Update existing devis
      devisId = existingDevis.id

      // Clear existing sections and lines
      await serviceSupabase.from("sections_devis").delete().eq("devis_id", devisId)
    } else {
      // Verify that the request exists and get projet_id
      const { data: requestData, error: requestError } = await serviceSupabase
        .from("requests")
        .select("id")
        .eq("id", requestId)
        .single()

      if (requestError || !requestData) {
        throw new Error(`Demande avec l'ID ${requestId} non trouvée`)
      }

      // Create new devis with service role client (bypasses RLS)
      const reference = `DEV-${Date.now()}`
      const devisData = {
        request_id: requestId,
        utilisateur_id: userId,
        reference,
        statut: "brouillon" as const,
        introduction: "Devis généré automatiquement par IA Gemini 2.5 Pro à partir de l'analyse technique",
        conclusion: "Merci de votre confiance. Ce devis a été optimisé par intelligence artificielle.",
        taux_remise: 0,
        montant_ht: 0,
        montant_ttc: 0,
        validite_jours: 30,
        conditions_acceptees: false,
        date_creation: new Date().toISOString(),
        date_validite: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
      }

      console.log("Création du devis avec les données:", devisData)

      const { data: newDevis, error: devisError } = await serviceSupabase
        .from("devis")
        .insert([devisData])
        .select()
        .single()

      if (devisError) {
        console.error("Erreur lors de la création du devis:", devisError)
        console.error("Détails de l'erreur:", devisError.details)
        console.error("Message d'erreur:", devisError.message)
        console.error("Code d'erreur:", devisError.code)
        throw devisError
      }

      devisId = newDevis.id
    }

    // Create sections and lines with enhanced validation
    for (const [sectionIndex, section] of devisStructure.sections.entries()) {
      if (!section.titre_section || !section.lignes_devis) {
        console.warn(`Section ${sectionIndex} invalide, ignorée`)
        continue
      }

      // Create section
      const { data: sectionData, error: sectionError } = await serviceSupabase
        .from("sections_devis")
        .insert([
          {
            devis_id: devisId,
            titre: section.titre_section,
            description: section.description || `Section générée automatiquement par IA Gemini 2.5 Pro`,
            ordre: sectionIndex + 1,
            montant_ht: 0,
          },
        ])
        .select()
        .single()

      if (sectionError) {
        console.error("Erreur lors de la création de la section:", sectionError)
        throw sectionError
      }

      // Create lines for this section with validation
      for (const [lineIndex, ligne] of section.lignes_devis.entries()) {
        if (!ligne.designation) {
          console.warn(`Ligne ${lineIndex} de la section ${section.titre_section} invalide, ignorée`)
          continue
        }

        const { error: lineError } = await serviceSupabase.from("lignes_devis").insert([
          {
            section_id: sectionData.id,
            designation: ligne.designation,
            reference: `REF-${Date.now()}-${sectionIndex}-${lineIndex}`,
            description: ligne.description || "",
            quantite: Number(ligne.quantite) || 1,
            unite: ligne.unite || "forfait",
            prix_unitaire_ht: Number(ligne.prix_unitaire_ht) || 0,
            taux_tva: Number(ligne.taux_tva) || 20,
            montant_ht: 0,
            montant_ttc: 0,
            ordre: lineIndex + 1,
          },
        ])

        if (lineError) {
          console.error("Erreur lors de la création de la ligne:", lineError)
          throw lineError
        }
      }
    }

    return {
      id: devisId,
      message: "Devis créé avec succès par IA Gemini 2.5 Pro",
      sectionsCount: devisStructure.sections.length,
    }
  } catch (error: any) {
    console.error("Erreur lors de la conversion en devis:", error)

    // Use enhanced error handling
    const errorMessage = handleAIError(error)
    throw new Error(errorMessage)
  }
}

// Fallback function to create a basic devis structure
function createFallbackDevisStructure(analysisText: string) {
  console.log("Création d'une structure de devis de base...")

  // Extract key elements from the analysis text
  const sections = []

  // Look for common BTP elements in the text
  const hasGrosOeuvre = /béton|fondation|maçonnerie|structure/i.test(analysisText)
  const hasEquipements = /équipement|matériel|outillage/i.test(analysisText)
  const hasMainOeuvre = /main.d.œuvre|ouvrier|chef|équipe/i.test(analysisText)

  if (hasGrosOeuvre) {
    sections.push({
      titre_section: "Gros Œuvre",
      description: "Travaux de gros œuvre identifiés dans l'analyse",
      lignes_devis: [
        {
          designation: "Travaux de gros œuvre",
          description: "Prestations de gros œuvre selon analyse technique",
          quantite: 1,
          unite: "forfait",
          prix_unitaire_ht: null,
          taux_tva: 20,
        },
      ],
    })
  }

  if (hasEquipements) {
    sections.push({
      titre_section: "Équipements et Matériels",
      description: "Équipements et matériels nécessaires",
      lignes_devis: [
        {
          designation: "Fourniture d'équipements",
          description: "Équipements et matériels selon analyse technique",
          quantite: 1,
          unite: "forfait",
          prix_unitaire_ht: null,
          taux_tva: 20,
        },
      ],
    })
  }

  if (hasMainOeuvre) {
    sections.push({
      titre_section: "Main d'Œuvre",
      description: "Ressources humaines nécessaires",
      lignes_devis: [
        {
          designation: "Main d'œuvre qualifiée",
          description: "Personnel qualifié selon analyse technique",
          quantite: 1,
          unite: "forfait",
          prix_unitaire_ht: null,
          taux_tva: 20,
        },
      ],
    })
  }

  // If no specific elements found, create a generic section
  if (sections.length === 0) {
    sections.push({
      titre_section: "Prestations issues de l'analyse IA",
      description: "Prestations identifiées par l'analyse technique",
      lignes_devis: [
        {
          designation: "Analyse et étude du projet",
          description: "Étude technique complète du projet",
          quantite: 1,
          unite: "forfait",
          prix_unitaire_ht: null,
          taux_tva: 20,
        },
      ],
    })
  }

  return { sections }
}
