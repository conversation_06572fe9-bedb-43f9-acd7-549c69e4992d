import { supabase } from "@/lib/supabase"
import { demoData, isDemoMode } from "@/lib/demo-mode"
import type { <PERSON><PERSON> } from "./devis"

export interface Facture {
  id: number
  devis_id: number | null
  reference: string
  type: "acompte" | "situation" | "solde"
  date_emission: string
  date_echeance: string
  montant_ht: number
  montant_ttc: number
  statut: "brouillon" | "envoyée" | "payée" | "impayée"
  date_paiement: string | null
  mode_paiement: string | null
  devis?: Devis
  client_nom?: string
}

// Données de démonstration pour les factures
const demoFactures = [
  {
    id: 1,
    devis_id: 1,
    reference: "FACT-2023-001",
    type: "acompte",
    date_emission: new Date().toISOString(),
    date_echeance: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
    montant_ht: 1050,
    montant_ttc: 1260,
    statut: "payée",
    date_paiement: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
    mode_paiement: "virement",
    client_nom: "<PERSON>pont Jean",
  },
  {
    id: 2,
    devis_id: 1,
    reference: "FACT-2023-002",
    type: "solde",
    date_emission: new Date().toISOString(),
    date_echeance: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
    montant_ht: 2450,
    montant_ttc: 2940,
    statut: "envoyée",
    date_paiement: null,
    mode_paiement: null,
    client_nom: "Dupont Jean",
  },
  {
    id: 3,
    devis_id: 2,
    reference: "FACT-2023-003",
    type: "acompte",
    date_emission: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000).toISOString(),
    date_echeance: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000).toISOString(),
    montant_ht: 840,
    montant_ttc: 1008,
    statut: "impayée",
    date_paiement: null,
    mode_paiement: null,
    client_nom: "Entreprise ABC",
  },
  {
    id: 4,
    devis_id: null,
    reference: "FACT-2023-004",
    type: "solde",
    date_emission: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
    date_echeance: new Date(Date.now() + 28 * 24 * 60 * 60 * 1000).toISOString(),
    montant_ht: 1200,
    montant_ttc: 1440,
    statut: "brouillon",
    date_paiement: null,
    mode_paiement: null,
    client_nom: "Martin Sophie",
  },
]

// Ajouter les factures aux données de démonstration
if (!demoData.factures) {
  demoData.factures = demoFactures
}

export async function getFactures() {
  // Si le mode démo est activé, retourner les données de démonstration
  if (isDemoMode()) {
    console.log("Mode démo activé, utilisation des données de démonstration pour les factures")
    return demoData.factures as Facture[]
  }

  try {
    // Récupérer les factures avec les informations du devis
    // Nous ne tentons plus de joindre directement clients depuis devis
    const { data, error } = await supabase
      .from("factures")
      .select(`
        *,
        devis:devis(*)
      `)
      .order("date_emission", { ascending: false })

    if (error) {
      console.error("Erreur Supabase:", error)
      throw error
    }

    // Transformer les données pour ajouter le nom du client
    // Pour une application réelle, vous devriez faire une requête séparée pour obtenir les informations du client
    const facturesWithClientName = data.map((facture) => {
      // Par défaut, utiliser un nom générique
      const clientNom = "Client associé au devis"

      return {
        ...facture,
        client_nom: clientNom,
      }
    })

    return facturesWithClientName as Facture[]
  } catch (error) {
    console.error("Erreur lors de la récupération des factures:", error)
    throw error
  }
}

export async function getFacture(id: number) {
  // Si le mode démo est activé, retourner les données de démonstration
  if (isDemoMode()) {
    const facture = demoData.factures.find((f) => f.id === id)
    if (!facture) {
      throw new Error("Facture non trouvée")
    }
    return facture as Facture
  }

  try {
    const { data, error } = await supabase
      .from("factures")
      .select(`
        *,
        devis:devis(*)
      `)
      .eq("id", id)
      .single()

    if (error) {
      console.error("Erreur Supabase:", error)
      throw error
    }

    // Ajouter un nom de client générique
    return {
      ...data,
      client_nom: "Client associé au devis",
    } as Facture
  } catch (error) {
    console.error("Erreur lors de la récupération de la facture:", error)
    throw error
  }
}

export async function createFacture(facture: Omit<Facture, "id" | "client_nom">) {
  // Si le mode démo est activé, simuler la création
  if (isDemoMode()) {
    console.log("Mode démo activé, simulation de création de facture")
    const factures = [...demoData.factures]
    const newFacture: Facture = {
      id: factures.length + 1,
      ...facture,
      client_nom: "Nouveau client",
    }
    return newFacture
  }

  try {
    const { data, error } = await supabase.from("factures").insert([facture]).select()

    if (error) {
      console.error("Erreur Supabase:", error)
      throw error
    }

    return data[0] as Facture
  } catch (error) {
    console.error("Erreur lors de la création de la facture:", error)
    throw error
  }
}

export async function updateFacture(id: number, facture: Partial<Omit<Facture, "id" | "client_nom">>) {
  // Si le mode démo est activé, simuler la mise à jour
  if (isDemoMode()) {
    console.log("Mode démo activé, simulation de mise à jour de facture")
    const factures = [...demoData.factures]
    const index = factures.findIndex((f) => f.id === id)
    if (index === -1) {
      throw new Error("Facture non trouvée")
    }
    const updatedFacture = { ...factures[index], ...facture }
    return updatedFacture as Facture
  }

  try {
    const { data, error } = await supabase.from("factures").update(facture).eq("id", id).select()

    if (error) {
      console.error("Erreur Supabase:", error)
      throw error
    }

    return data[0] as Facture
  } catch (error) {
    console.error("Erreur lors de la mise à jour de la facture:", error)
    throw error
  }
}

export async function deleteFacture(id: number) {
  // Si le mode démo est activé, simuler la suppression
  if (isDemoMode()) {
    console.log("Mode démo activé, simulation de suppression de facture")
    return true
  }

  try {
    const { error } = await supabase.from("factures").delete().eq("id", id)

    if (error) {
      console.error("Erreur Supabase:", error)
      throw error
    }

    return true
  } catch (error) {
    console.error("Erreur lors de la suppression de la facture:", error)
    throw error
  }
}
