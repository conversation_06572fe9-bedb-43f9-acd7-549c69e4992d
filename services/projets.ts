import { supabase } from "@/lib/supabase"
import { demoData, isDemoMode } from "@/lib/demo-mode"
import type { Client } from "./clients"

export interface Projet {
  id: number
  client_id: number
  utilisateur_id: number
  reference: string
  nom: string
  adresse_chantier: string
  date_creation: string
  date_debut_prevu: string | null
  date_fin_prevu: string | null
  date_debut_reel: string | null
  date_fin_reel: string | null
  statut: "devis" | "signé" | "en_cours" | "terminé" | "facturé"
  description: string
  montant_total_ht: number | null
  montant_total_ttc: number | null
  client?: Client
}

// Données de démonstration pour les projets
const demoProjets = [
  {
    id: 1,
    client_id: 1,
    utilisateur_id: 1,
    reference: "PROJ-2023-001",
    nom: "Rénovation salle de bain",
    adresse_chantier: "456 avenue du Client, 75002 Paris",
    date_creation: new Date().toISOString(),
    date_debut_prevu: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
    date_fin_prevu: new Date(Date.now() + 21 * 24 * 60 * 60 * 1000).toISOString(),
    date_debut_reel: null,
    date_fin_reel: null,
    statut: "devis",
    description: "Rénovation complète de la salle de bain",
    montant_total_ht: 3500,
    montant_total_ttc: 4200,
    client: {
      id: 1,
      entreprise_id: 1,
      type: "particulier",
      nom: "Dupont",
      prenom: "Jean",
      societe: null,
      siret: null,
      adresse_facturation: "456 avenue du Client, 75002 Paris",
      adresse_chantier: null,
      telephone: "06 12 34 56 78",
      email: "<EMAIL>",
      notes: null,
      date_creation: new Date().toISOString(),
      actif: true,
    },
  },
  {
    id: 2,
    client_id: 2,
    utilisateur_id: 1,
    reference: "PROJ-2023-002",
    nom: "Construction mur de clôture",
    adresse_chantier: "789 boulevard Pro, 75002 Paris",
    date_creation: new Date().toISOString(),
    date_debut_prevu: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000).toISOString(),
    date_fin_prevu: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
    date_debut_reel: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000).toISOString(),
    date_fin_reel: null,
    statut: "en_cours",
    description: "Construction d'un mur de clôture en parpaings",
    montant_total_ht: 2800,
    montant_total_ttc: 3360,
    client: {
      id: 2,
      entreprise_id: 1,
      type: "professionnel",
      nom: null,
      prenom: null,
      societe: "Entreprise ABC",
      siret: "98765432109876",
      adresse_facturation: "789 boulevard Pro, 75002 Paris",
      adresse_chantier: null,
      telephone: "01 87 65 43 21",
      email: "<EMAIL>",
      notes: null,
      date_creation: new Date().toISOString(),
      actif: true,
    },
  },
  {
    id: 3,
    client_id: 1,
    utilisateur_id: 2,
    reference: "PROJ-2023-003",
    nom: "Réfection toiture",
    adresse_chantier: "456 avenue du Client, 75002 Paris",
    date_creation: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000).toISOString(),
    date_debut_prevu: new Date(Date.now() - 45 * 24 * 60 * 60 * 1000).toISOString(),
    date_fin_prevu: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
    date_debut_reel: new Date(Date.now() - 45 * 24 * 60 * 60 * 1000).toISOString(),
    date_fin_reel: new Date(Date.now() - 28 * 24 * 60 * 60 * 1000).toISOString(),
    statut: "terminé",
    description: "Réfection complète de la toiture",
    montant_total_ht: 8500,
    montant_total_ttc: 10200,
    client: {
      id: 1,
      entreprise_id: 1,
      type: "particulier",
      nom: "Dupont",
      prenom: "Jean",
      societe: null,
      siret: null,
      adresse_facturation: "456 avenue du Client, 75002 Paris",
      adresse_chantier: null,
      telephone: "06 12 34 56 78",
      email: "<EMAIL>",
      notes: null,
      date_creation: new Date().toISOString(),
      actif: true,
    },
  },
]

// Ajouter les projets aux données de démonstration
if (!demoData.projets) {
  demoData.projets = demoProjets
}

export async function getProjets() {
  // Si le mode démo est activé, retourner les données de démonstration
  if (isDemoMode()) {
    console.log("Mode démo activé, utilisation des données de démonstration pour les projets")
    return demoData.projets as Projet[]
  }

  try {
    // Récupérer les projets avec les informations du client
    const { data, error } = await supabase
      .from("projets")
      .select(`
        *,
        client:clients(*)
      `)
      .order("date_creation", { ascending: false })

    if (error) {
      console.error("Erreur Supabase:", error)
      throw error
    }

    return data as Projet[]
  } catch (error) {
    console.error("Erreur lors de la récupération des projets:", error)
    throw error
  }
}

export async function getProjet(id: number) {
  // Si le mode démo est activé, retourner les données de démonstration
  if (isDemoMode()) {
    const projet = demoData.projets.find((p) => p.id === id)
    if (!projet) {
      throw new Error("Projet non trouvé")
    }
    return projet as Projet
  }

  try {
    const { data, error } = await supabase
      .from("projets")
      .select(`
        *,
        client:clients(*)
      `)
      .eq("id", id)
      .single()

    if (error) {
      console.error("Erreur Supabase:", error)
      throw error
    }

    return data as Projet
  } catch (error) {
    console.error("Erreur lors de la récupération du projet:", error)
    throw error
  }
}

export async function createProjet(projet: Omit<Projet, "id" | "date_creation" | "client">) {
  // Si le mode démo est activé, simuler la création
  if (isDemoMode()) {
    console.log("Mode démo activé, simulation de création de projet")
    const projets = [...demoData.projets]
    const newProjet: Projet = {
      id: projets.length + 1,
      ...projet,
      date_creation: new Date().toISOString(),
      client: undefined,
    }
    return newProjet
  }

  try {
    const { data, error } = await supabase.from("projets").insert([projet]).select()

    if (error) {
      console.error("Erreur Supabase:", error)
      throw error
    }

    return data[0] as Projet
  } catch (error) {
    console.error("Erreur lors de la création du projet:", error)
    throw error
  }
}

export async function updateProjet(id: number, projet: Partial<Omit<Projet, "id" | "client">>) {
  // Si le mode démo est activé, simuler la mise à jour
  if (isDemoMode()) {
    console.log("Mode démo activé, simulation de mise à jour de projet")
    const projets = [...demoData.projets]
    const index = projets.findIndex((p) => p.id === id)
    if (index === -1) {
      throw new Error("Projet non trouvé")
    }
    const updatedProjet = { ...projets[index], ...projet }
    return updatedProjet as Projet
  }

  try {
    const { data, error } = await supabase.from("projets").update(projet).eq("id", id).select()

    if (error) {
      console.error("Erreur Supabase:", error)
      throw error
    }

    return data[0] as Projet
  } catch (error) {
    console.error("Erreur lors de la mise à jour du projet:", error)
    throw error
  }
}

export async function deleteProjet(id: number) {
  // Si le mode démo est activé, simuler la suppression
  if (isDemoMode()) {
    console.log("Mode démo activé, simulation de suppression de projet")
    return true
  }

  try {
    const { error } = await supabase.from("projets").delete().eq("id", id)

    if (error) {
      console.error("Erreur Supabase:", error)
      throw error
    }

    return true
  } catch (error) {
    console.error("Erreur lors de la suppression du projet:", error)
    throw error
  }
}
