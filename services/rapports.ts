import { supabase } from "@/lib/supabase"
import { isDemoMode } from "@/lib/demo-mode"

export type Rapport = {
  id: string
  projet_id: string
  titre: string
  date_creation: string
  contenu: string
  statut: string
  projet?: {
    id: string
    nom: string
    client_id: string
    client?: {
      id: string
      nom: string
      prenom: string
    }
  }
}

// Demo data for rapports
const demoRapports: Rapport[] = [
  {
    id: "1",
    projet_id: "1",
    titre: "Rapport initial - Fondations",
    date_creation: "2023-05-15",
    contenu:
      "Les travaux de fondation ont débuté. Le terrain a été préparé et les premières excavations sont terminées.",
    statut: "Terminé",
    projet: {
      id: "1",
      nom: "Rénovation Mai<PERSON>",
      client_id: "1",
      client: {
        id: "1",
        nom: "Dupont",
        prenom: "Jean",
      },
    },
  },
  {
    id: "2",
    projet_id: "1",
    titre: "Rapport d'avancement - Structure",
    date_creation: "2023-06-10",
    contenu:
      "La structure principale est en place. Les murs porteurs sont érigés et le toit est en cours d'installation.",
    statut: "En cours",
    projet: {
      id: "1",
      nom: "Rénovation Maison Dupont",
      client_id: "1",
      client: {
        id: "1",
        nom: "Dupont",
        prenom: "Jean",
      },
    },
  },
  {
    id: "3",
    projet_id: "2",
    titre: "Rapport initial - Terrassement",
    date_creation: "2023-07-05",
    contenu:
      "Le terrassement a commencé. Les travaux avancent comme prévu malgré quelques difficultés liées au sol rocheux.",
    statut: "En cours",
    projet: {
      id: "2",
      nom: "Construction Immeuble Martin",
      client_id: "2",
      client: {
        id: "2",
        nom: "Martin",
        prenom: "Sophie",
      },
    },
  },
]

export async function getRapports() {
  if (isDemoMode()) {
    return { data: demoRapports, error: null }
  }

  try {
    // Simplified query that doesn't rely on relationships
    const { data, error } = await supabase.from("rapports").select("*").order("date_creation", { ascending: false })

    if (error) {
      console.error("Erreur Supabase:", error.message)
      return { data: [], error: error.message }
    }

    // Add placeholder project information
    const rapportsWithProjects = data.map((rapport) => ({
      ...rapport,
      projet: {
        nom: "Projet associé",
        client: {
          prenom: "Client",
          nom: "Associé",
        },
      },
    }))

    return { data: rapportsWithProjects, error: null }
  } catch (error) {
    console.error("Erreur lors de la récupération des rapports:", error)
    return { data: [], error: "Erreur lors de la récupération des rapports" }
  }
}

export async function getRapport(id: string) {
  if (isDemoMode()) {
    const rapport = demoRapports.find((r) => r.id === id)
    return { data: rapport, error: null }
  }

  try {
    // Simplified query that doesn't rely on relationships
    const { data, error } = await supabase.from("rapports").select("*").eq("id", id).single()

    if (error) {
      console.error("Erreur Supabase:", error.message)
      return { data: null, error: error.message }
    }

    // Add placeholder project information
    const rapportWithProject = {
      ...data,
      projet: {
        nom: "Projet associé",
        client: {
          prenom: "Client",
          nom: "Associé",
        },
      },
    }

    return { data: rapportWithProject, error: null }
  } catch (error) {
    console.error("Erreur lors de la récupération du rapport:", error)
    return { data: null, error: "Erreur lors de la récupération du rapport" }
  }
}

export async function createRapport(rapport: Omit<Rapport, "id">) {
  if (isDemoMode()) {
    const newRapport = {
      ...rapport,
      id: (demoRapports.length + 1).toString(),
    }
    demoRapports.push(newRapport as Rapport)
    return { data: newRapport, error: null }
  }

  try {
    // Extract only the fields that belong to the rapports table
    const { projet_id, titre, date_creation, contenu, statut } = rapport
    const rapportData = { projet_id, titre, date_creation, contenu, statut }

    const { data, error } = await supabase.from("rapports").insert(rapportData).select().single()

    if (error) {
      console.error("Erreur Supabase:", error.message)
      return { data: null, error: error.message }
    }

    return { data, error: null }
  } catch (error) {
    console.error("Erreur lors de la création du rapport:", error)
    return { data: null, error: "Erreur lors de la création du rapport" }
  }
}

export async function updateRapport(id: string, rapport: Partial<Rapport>) {
  if (isDemoMode()) {
    const index = demoRapports.findIndex((r) => r.id === id)
    if (index !== -1) {
      demoRapports[index] = { ...demoRapports[index], ...rapport }
      return { data: demoRapports[index], error: null }
    }
    return { data: null, error: "Rapport non trouvé" }
  }

  try {
    // Extract only the fields that belong to the rapports table
    const { projet_id, titre, date_creation, contenu, statut } = rapport
    const rapportData: any = {}
    if (projet_id !== undefined) rapportData.projet_id = projet_id
    if (titre !== undefined) rapportData.titre = titre
    if (date_creation !== undefined) rapportData.date_creation = date_creation
    if (contenu !== undefined) rapportData.contenu = contenu
    if (statut !== undefined) rapportData.statut = statut

    const { data, error } = await supabase.from("rapports").update(rapportData).eq("id", id).select().single()

    if (error) {
      console.error("Erreur Supabase:", error.message)
      return { data: null, error: error.message }
    }

    return { data, error: null }
  } catch (error) {
    console.error("Erreur lors de la mise à jour du rapport:", error)
    return { data: null, error: "Erreur lors de la mise à jour du rapport" }
  }
}

export async function deleteRapport(id: string) {
  if (isDemoMode()) {
    const index = demoRapports.findIndex((r) => r.id === id)
    if (index !== -1) {
      demoRapports.splice(index, 1)
      return { error: null }
    }
    return { error: "Rapport non trouvé" }
  }

  try {
    const { error } = await supabase.from("rapports").delete().eq("id", id)

    if (error) {
      console.error("Erreur Supabase:", error.message)
      return { error: error.message }
    }

    return { error: null }
  } catch (error) {
    console.error("Erreur lors de la suppression du rapport:", error)
    return { error: "Erreur lors de la suppression du rapport" }
  }
}
