import { supabase } from "@/lib/supabase"

export interface BucketVerificationResult {
  exists: boolean
  canUpload: boolean
  canList: boolean
  canDelete: boolean
  error?: string
  clientInitialized: boolean
}

/**
 * Verifies that the storage bucket exists and has the correct permissions
 */
export async function verifyBucket(bucketName = "client-files"): Promise<BucketVerificationResult> {
  const result: BucketVerificationResult = {
    exists: false,
    canUpload: false,
    canList: false,
    canDelete: false,
    clientInitialized: false,
  }

  // Check if Supabase client is initialized
  if (!supabase) {
    result.error = "Le client Supabase n'est pas initialisé."
    return result
  }

  // Check if auth is initialized
  if (!supabase.auth) {
    result.error = "L'objet auth de Supabase n'est pas initialisé."
    return result
  }

  result.clientInitialized = true

  try {
    // Step 1: Check if bucket exists by listing files
    const { data: listData, error: listError } = await supabase.storage.from(bucketName).list()

    if (listError) {
      if (listError.message?.includes("bucket not found") || listError.message?.includes("Bucket not found")) {
        result.error = `Le bucket '${bucketName}' n'existe pas.`
        return result
      }

      result.error = `Erreur lors de la vérification du bucket: ${listError.message}`
      return result
    }

    // Bucket exists if we get here
    result.exists = true
    result.canList = true

    // Step 2: Try to upload a test file
    const testContent = new Blob(["test"], { type: "text/plain" })
    const testFile = new File([testContent], "test-file.txt", { type: "text/plain" })
    const testPath = `verification/test-${Date.now()}.txt`

    const { data: uploadData, error: uploadError } = await supabase.storage.from(bucketName).upload(testPath, testFile)

    if (uploadError) {
      result.error = `Le bucket existe mais l'upload a échoué: ${uploadError.message}`
      return result
    }

    result.canUpload = true

    // Step 3: Try to delete the test file
    const { error: deleteError } = await supabase.storage.from(bucketName).remove([testPath])

    if (deleteError) {
      result.error = `Le bucket existe, l'upload fonctionne, mais la suppression a échoué: ${deleteError.message}`
      return result
    }

    result.canDelete = true

    return result
  } catch (error: any) {
    result.error = `Erreur lors de la vérification du bucket: ${error.message || "Erreur inconnue"}`
    return result
  }
}
