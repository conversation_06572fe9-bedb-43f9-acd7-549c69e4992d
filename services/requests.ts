import { supabase } from "@/lib/supabase"
import { demoData, isDemoMode } from "@/lib/demo-mode"

export interface Request {
  id: number
  client_id: number
  description: string
  status: "nouveau" | "en_analyse" | "accepté" | "refusé" | "en_cours" | "terminé"
  created_at: string
  updated_at: string
  client?: {
    id: number
    nom: string
    prenom: string
    societe: string
    type: string
  }
}

export interface Plan {
  id: number
  request_id: number
  file_path: string
  file_name: string
  file_type: string
  description: string | null
  uploaded_at: string
}

export interface Photo {
  id: number
  request_id: number
  file_path: string
  file_name: string
  description: string | null
  uploaded_at: string
}

// Get bucket name from environment variable
const BUCKET_NAME = process.env.NEXT_PUBLIC_SUPABASE_CLIENT_FILES_BUCKET || "client-files"

// Direct test function to verify bucket access
export async function testBucketAccess() {
  if (isDemoMode()) {
    return { success: true, message: "Mode démo: simulation de bucket fonctionnel" }
  }

  console.log(`Testing bucket access for: ${BUCKET_NAME}`)

  try {
    // First, check if we can list buckets
    console.log("Listing all buckets...")
    const { data: buckets, error: bucketsError } = await supabase.storage.listBuckets()

    if (bucketsError) {
      console.error("Error listing buckets:", bucketsError)
      return {
        success: false,
        message: `Erreur lors de la récupération des buckets: ${bucketsError.message}`,
        error: bucketsError,
      }
    }

    console.log(
      `Found ${buckets.length} buckets:`,
      buckets.map((b) => b.name),
    )

    // Check if our bucket exists in the list
    const bucketExists = buckets.some((bucket) => bucket.name === BUCKET_NAME)

    if (!bucketExists) {
      console.error(`Bucket '${BUCKET_NAME}' not found in list of buckets`)
      return {
        success: false,
        message: `Le bucket '${BUCKET_NAME}' n'existe pas dans la liste des buckets disponibles`,
        availableBuckets: buckets.map((b) => b.name),
        bucketName: BUCKET_NAME,
      }
    }

    // Try to list files in the bucket to check access
    console.log(`Listing files in bucket: ${BUCKET_NAME}`)
    const { data, error } = await supabase.storage.from(BUCKET_NAME).list()

    if (error) {
      console.error(`Error listing files in bucket '${BUCKET_NAME}':`, error)
      return {
        success: false,
        message: `Erreur lors de l'accès au bucket '${BUCKET_NAME}': ${error.message}`,
        error,
        bucketName: BUCKET_NAME,
      }
    }

    console.log(`Successfully listed files in bucket '${BUCKET_NAME}'`)

    // Try to create a test file to verify write access
    const testContent = new Blob(["test"], { type: "text/plain" })
    const testFileName = `test-${Date.now()}.txt`
    console.log(`Uploading test file to bucket: ${BUCKET_NAME}/${testFileName}`)

    const { data: uploadData, error: uploadError } = await supabase.storage
      .from(BUCKET_NAME)
      .upload(testFileName, testContent)

    if (uploadError) {
      console.error(`Error uploading test file to bucket '${BUCKET_NAME}':`, uploadError)
      return {
        success: false,
        message: `Erreur lors de l'upload d'un fichier test dans le bucket '${BUCKET_NAME}': ${uploadError.message}`,
        error: uploadError,
        bucketName: BUCKET_NAME,
      }
    }

    console.log(`Successfully uploaded test file to bucket '${BUCKET_NAME}'`)

    // Try to delete the test file to verify delete access
    console.log(`Deleting test file from bucket: ${BUCKET_NAME}/${testFileName}`)
    const { error: deleteError } = await supabase.storage.from(BUCKET_NAME).remove([testFileName])

    if (deleteError) {
      console.error(`Error deleting test file from bucket '${BUCKET_NAME}':`, deleteError)
      return {
        success: false,
        message: `Erreur lors de la suppression du fichier test dans le bucket '${BUCKET_NAME}': ${deleteError.message}`,
        error: deleteError,
        bucketName: BUCKET_NAME,
      }
    }

    console.log(`Successfully deleted test file from bucket '${BUCKET_NAME}'`)

    return {
      success: true,
      message: `Le bucket '${BUCKET_NAME}' est accessible et fonctionnel`,
      bucketName: BUCKET_NAME,
    }
  } catch (error: any) {
    console.error("Error testing bucket access:", error)
    return {
      success: false,
      message: `Erreur lors du test d'accès au bucket '${BUCKET_NAME}': ${error.message}`,
      error,
      bucketName: BUCKET_NAME,
    }
  }
}

// Check if the storage bucket exists and is accessible
export async function checkStorageBucketExists() {
  if (isDemoMode()) {
    return true
  }

  try {
    // First, check if we can list buckets
    const { data: buckets, error: bucketsError } = await supabase.storage.listBuckets()

    if (bucketsError) {
      console.error("Erreur lors de la récupération des buckets:", bucketsError)
      return false
    }

    // Check if our bucket exists in the list
    const bucketExists = buckets.some((bucket) => bucket.name === BUCKET_NAME)

    if (!bucketExists) {
      console.error(`Le bucket '${BUCKET_NAME}' n'existe pas`)
      return false
    }

    // Try to list files in the bucket to check access
    const { data, error } = await supabase.storage.from(BUCKET_NAME).list()

    if (error) {
      console.error(`Erreur lors de l'accès au bucket '${BUCKET_NAME}':`, error)
      return false
    }

    return true
  } catch (error) {
    console.error("Erreur lors de la vérification du bucket:", error)
    return false
  }
}

// Get all requests with client information
export async function getRequests() {
  if (isDemoMode()) {
    return demoData.requests || []
  }

  try {
    const { data, error } = await supabase
      .from("requests")
      .select(`
        *,
        client:clients(id, nom, prenom, societe, type)
      `)
      .order("created_at", { ascending: false })

    if (error) {
      console.error("Erreur Supabase:", error)
      throw error
    }

    return data as (Request & { client: Request["client"] })[]
  } catch (error) {
    console.error("Erreur lors de la récupération des demandes:", error)
    throw error
  }
}

// Get a single request with client information, plans, and photos
export async function getRequest(id: number) {
  // Validate the ID
  if (!id || isNaN(id)) {
    throw new Error("ID de demande invalide")
  }

  if (isDemoMode()) {
    const request = demoData.requests?.find((r) => r.id === id)
    if (!request) throw new Error("Demande non trouvée")
    return {
      ...request,
      plans: demoData.plans?.filter((p) => p.request_id === id) || [],
      photos: demoData.photos?.filter((p) => p.request_id === id) || [],
    }
  }

  try {
    // Get the request with client information
    const { data: request, error: requestError } = await supabase
      .from("requests")
      .select(`
        *,
        client:clients(id, nom, prenom, societe, type)
      `)
      .eq("id", id)
      .single()

    if (requestError) {
      console.error("Erreur Supabase (demande):", requestError)
      throw requestError
    }

    // Get plans for this request
    const { data: plans, error: plansError } = await supabase
      .from("plans")
      .select("*")
      .eq("request_id", id)
      .order("uploaded_at", { ascending: false })

    if (plansError) {
      console.error("Erreur Supabase (plans):", plansError)
      throw plansError
    }

    // Get photos for this request
    const { data: photos, error: photosError } = await supabase
      .from("photos")
      .select("*")
      .eq("request_id", id)
      .order("uploaded_at", { ascending: false })

    if (photosError) {
      console.error("Erreur Supabase (photos):", photosError)
      throw photosError
    }

    return {
      ...request,
      plans,
      photos,
    }
  } catch (error) {
    console.error("Erreur lors de la récupération de la demande:", error)
    throw error
  }
}

// Create a new request
export async function createRequest(requestData: {
  client_id: number
  description: string
  status?: Request["status"]
}) {
  if (isDemoMode()) {
    console.log("Mode démo: simulation de création de demande")
    return { id: Math.floor(Math.random() * 1000) + 1 }
  }

  try {
    const { data, error } = await supabase.from("requests").insert([requestData]).select()

    if (error) {
      console.error("Erreur Supabase:", error)
      throw error
    }

    return data[0] as Request
  } catch (error) {
    console.error("Erreur lors de la création de la demande:", error)
    throw error
  }
}

// Update a request
export async function updateRequest(id: number, requestData: Partial<Request>) {
  // Validate the ID
  if (!id || isNaN(id)) {
    throw new Error("ID de demande invalide")
  }

  if (isDemoMode()) {
    console.log("Mode démo: simulation de mise à jour de demande")
    return { id }
  }

  try {
    const { data, error } = await supabase.from("requests").update(requestData).eq("id", id).select()

    if (error) {
      console.error("Erreur Supabase:", error)
      throw error
    }

    return data[0] as Request
  } catch (error) {
    console.error("Erreur lors de la mise à jour de la demande:", error)
    throw error
  }
}

const MAX_RETRIES = 3
const UPLOAD_TIMEOUT = 10000 // 10 seconds

// Upload a plan
export async function uploadPlan(requestId: number, file: File, description?: string, retryCount = 0): Promise<Plan> {
  // Validate the request ID
  if (!requestId || isNaN(requestId)) {
    throw new Error("ID de demande invalide")
  }

  if (isDemoMode()) {
    console.log("Mode démo: simulation d'upload de plan")
    return { id: Math.floor(Math.random() * 1000) + 1 } as Plan
  }

  try {
    // Check if bucket exists and is accessible
    const bucketExists = await checkStorageBucketExists()
    if (!bucketExists) {
      throw new Error(
        `Le bucket '${BUCKET_NAME}' n'existe pas ou n'est pas accessible. Veuillez le créer dans la console Supabase.`,
      )
    }

    // Create folder structure if it doesn't exist
    const folderPath = `plans/${requestId}`

    // Upload file to Supabase Storage with detailed error handling
    const fileName = `${Date.now()}_${file.name.replace(/[^a-zA-Z0-9.-]/g, "_")}`
    const filePath = `${folderPath}/${fileName}`

    console.log(`Uploading file to ${BUCKET_NAME}/${filePath}`)

    // Use AbortController to implement timeout
    const controller = new AbortController()
    const timeoutId = setTimeout(() => {
      controller.abort()
    }, UPLOAD_TIMEOUT)

    const { data: fileData, error: fileError } = await supabase.storage.from(BUCKET_NAME).upload(filePath, file, {
      cacheControl: "3600",
      upsert: false,
      signal: controller.signal, // Pass the AbortSignal
    })

    clearTimeout(timeoutId) // Clear the timeout if the upload completes

    if (fileError) {
      console.error("Erreur Supabase Storage:", fileError)

      // Provide more detailed error information
      if (fileError.message?.includes("bucket not found") || fileError.message?.includes("Bucket not found")) {
        throw new Error(`Le bucket '${BUCKET_NAME}' n'a pas été trouvé. Veuillez le créer dans la console Supabase.`)
      }

      if (fileError.message?.includes("permission denied") || fileError.message?.includes("not authorized")) {
        throw new Error(
          `Permissions insuffisantes pour accéder au bucket '${BUCKET_NAME}'. Vérifiez les politiques d'accès.`,
        )
      }

      // Retry the upload if it failed due to a network error and we haven't reached the max retries
      if (fileError.message?.includes("Failed to fetch") && retryCount < MAX_RETRIES) {
        console.log(`Upload failed, retrying (${retryCount + 1}/${MAX_RETRIES})...`)
        return await uploadPlan(requestId, file, description, retryCount + 1)
      }

      throw fileError
    }

    // Create record in plans table
    const { data, error } = await supabase
      .from("plans")
      .insert([
        {
          request_id: requestId,
          file_path: filePath,
          file_name: file.name,
          file_type: file.type,
          description: description || null,
        },
      ])
      .select()

    if (error) {
      console.error("Erreur Supabase:", error)
      throw error
    }

    return data[0] as Plan
  } catch (error: any) {
    console.error("Erreur lors de l'upload du plan:", error)
    throw error
  }
}

// Upload a photo
export async function uploadPhoto(requestId: number, file: File, description?: string) {
  // Validate the request ID
  if (!requestId || isNaN(requestId)) {
    throw new Error("ID de demande invalide")
  }

  if (isDemoMode()) {
    console.log("Mode démo: simulation d'upload de photo")
    return { id: Math.floor(Math.random() * 1000) + 1 }
  }

  try {
    // Check if bucket exists and is accessible
    const bucketExists = await checkStorageBucketExists()
    if (!bucketExists) {
      throw new Error(
        `Le bucket '${BUCKET_NAME}' n'existe pas ou n'est pas accessible. Veuillez le créer dans la console Supabase.`,
      )
    }

    // Create folder structure if it doesn't exist
    const folderPath = `photos/${requestId}`

    // Upload file to Supabase Storage with detailed error handling
    const fileName = `${Date.now()}_${file.name.replace(/[^a-zA-Z0-9.-]/g, "_")}`
    const filePath = `${folderPath}/${fileName}`

    console.log(`Uploading file to ${BUCKET_NAME}/${filePath}`)

    const { data: fileData, error: fileError } = await supabase.storage.from(BUCKET_NAME).upload(filePath, file, {
      cacheControl: "3600",
      upsert: false,
    })

    if (fileError) {
      console.error("Erreur Supabase Storage:", fileError)

      // Provide more detailed error information
      if (fileError.message?.includes("bucket not found") || fileError.message?.includes("Bucket not found")) {
        throw new Error(`Le bucket '${BUCKET_NAME}' n'a pas été trouvé. Veuillez le créer dans la console Supabase.`)
      }

      if (fileError.message?.includes("permission denied") || fileError.message?.includes("not authorized")) {
        throw new Error(
          `Permissions insuffisantes pour accéder au bucket '${BUCKET_NAME}'. Vérifiez les politiques d'accès.`,
        )
      }

      throw fileError
    }

    // Create record in photos table
    const { data, error } = await supabase
      .from("photos")
      .insert([
        {
          request_id: requestId,
          file_path: filePath,
          file_name: file.name,
          description: description || null,
        },
      ])
      .select()

    if (error) {
      console.error("Erreur Supabase:", error)
      throw error
    }

    return data[0] as Photo
  } catch (error) {
    console.error("Erreur lors de l'upload de la photo:", error)
    throw error
  }
}

// Delete a plan
export async function deletePlan(id: number) {
  // Validate the ID
  if (!id || isNaN(id)) {
    throw new Error("ID de plan invalide")
  }

  if (isDemoMode()) {
    console.log("Mode démo: simulation de suppression de plan")
    return true
  }

  try {
    // Get the plan to find the file path
    const { data: plan, error: getError } = await supabase.from("plans").select("file_path").eq("id", id).single()

    if (getError) {
      console.error("Erreur Supabase:", getError)
      throw getError
    }

    // Delete the file from storage
    const { error: storageError } = await supabase.storage.from(BUCKET_NAME).remove([plan.file_path])

    if (storageError) {
      console.error("Erreur Supabase Storage:", storageError)
      throw storageError
    }

    // Delete the record from the plans table
    const { error } = await supabase.from("plans").delete().eq("id", id)

    if (error) {
      console.error("Erreur Supabase:", error)
      throw error
    }

    return true
  } catch (error) {
    console.error("Erreur lors de la suppression du plan:", error)
    throw error
  }
}

// Delete a photo
export async function deletePhoto(id: number) {
  // Validate the ID
  if (!id || isNaN(id)) {
    throw new Error("ID de photo invalide")
  }

  if (isDemoMode()) {
    console.log("Mode démo: simulation de suppression de photo")
    return true
  }

  try {
    // Get the photo to find the file path
    const { data: photo, error: getError } = await supabase.from("photos").select("file_path").eq("id", id).single()

    if (getError) {
      console.error("Erreur Supabase:", getError)
      throw getError
    }

    // Delete the file from storage
    const { error: storageError } = await supabase.storage.from(BUCKET_NAME).remove([photo.file_path])

    if (storageError) {
      console.error("Erreur Supabase Storage:", storageError)
      throw storageError
    }

    // Delete the record from the photos table
    const { error } = await supabase.from("photos").delete().eq("id", id)

    if (error) {
      console.error("Erreur Supabase:", error)
      throw error
    }

    return true
  } catch (error) {
    console.error("Erreur lors de la suppression de la photo:", error)
    throw error
  }
}
