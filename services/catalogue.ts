import { supabase } from "@/lib/supabase"
import { demoData, isDemoMode } from "@/lib/demo-mode"

export interface CatalogueItem {
  id: number
  entreprise_id: number
  nom: string
  type: "matériaux" | "main d'œuvre" | "prestation"
  reference: string
  description: string
  prix_unitaire_ht: number
  unite: string
  taux_tva: number
  actif: boolean
  categorie: string
  sous_categorie: string
}

export async function getCatalogueItems() {
  // Si le mode démo est activé, retourner les données de démonstration
  if (isDemoMode()) {
    console.log("Mode démo activé, utilisation des données de démonstration")
    return demoData.catalogue as CatalogueItem[]
  }

  try {
    const { data, error } = await supabase.from("catalogue").select("*").order("nom", { ascending: true })

    if (error) {
      console.error("Erreur Supabase:", error)
      throw error
    }

    return data as CatalogueItem[]
  } catch (error) {
    console.error("Erreur lors de la récupération du catalogue:", error)
    throw error
  }
}

export async function getCatalogueItem(id: number) {
  // Si le mode démo est activé, retourner les données de démonstration
  if (isDemoMode()) {
    const item = demoData.catalogue.find((i) => i.id === id)
    if (!item) {
      throw new Error("Élément non trouvé")
    }
    return item as CatalogueItem
  }

  try {
    const { data, error } = await supabase.from("catalogue").select("*").eq("id", id).single()

    if (error) {
      console.error("Erreur Supabase:", error)
      throw error
    }

    return data as CatalogueItem
  } catch (error) {
    console.error("Erreur lors de la récupération de l'élément:", error)
    throw error
  }
}

export async function createCatalogueItem(item: Omit<CatalogueItem, "id">) {
  // Si le mode démo est activé, simuler la création
  if (isDemoMode()) {
    console.log("Mode démo activé, simulation de création d'élément de catalogue")
    const items = [...demoData.catalogue]
    const newItem: CatalogueItem = {
      id: items.length + 1,
      ...item,
    }
    return newItem
  }

  try {
    const { data, error } = await supabase.from("catalogue").insert([item]).select()

    if (error) {
      console.error("Erreur Supabase:", error)
      throw error
    }

    return data[0] as CatalogueItem
  } catch (error) {
    console.error("Erreur lors de la création de l'élément:", error)
    throw error
  }
}

export async function updateCatalogueItem(id: number, item: Partial<CatalogueItem>) {
  // Si le mode démo est activé, simuler la mise à jour
  if (isDemoMode()) {
    console.log("Mode démo activé, simulation de mise à jour d'élément de catalogue")
    const items = [...demoData.catalogue]
    const index = items.findIndex((i) => i.id === id)
    if (index === -1) {
      throw new Error("Élément non trouvé")
    }
    const updatedItem = { ...items[index], ...item }
    return updatedItem as CatalogueItem
  }

  try {
    const { data, error } = await supabase.from("catalogue").update(item).eq("id", id).select()

    if (error) {
      console.error("Erreur Supabase:", error)
      throw error
    }

    return data[0] as CatalogueItem
  } catch (error) {
    console.error("Erreur lors de la mise à jour de l'élément:", error)
    throw error
  }
}

export async function deleteCatalogueItem(id: number) {
  // Si le mode démo est activé, simuler la suppression
  if (isDemoMode()) {
    console.log("Mode démo activé, simulation de suppression d'élément de catalogue")
    return true
  }

  try {
    const { error } = await supabase.from("catalogue").delete().eq("id", id)

    if (error) {
      console.error("Erreur Supabase:", error)
      throw error
    }

    return true
  } catch (error) {
    console.error("Erreur lors de la suppression de l'élément:", error)
    throw error
  }
}
